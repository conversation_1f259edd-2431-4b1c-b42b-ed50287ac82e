=== 🚀 启动DFM训练管道 ===
目标变量: 工业增加值同比增速_月度_同花顺
选择的指标数量: 5
🔥 因子选择范围: (1, 20)
🔥 启用变量选择: False
🔥 启用超参数调优: False

=== 📋 参数传递验证 ===
📅 时间参数:
  - 训练开始日期: 2020-01-03
  - 训练结束日期: None
  - 验证开始日期: 2024-01-05
  - 验证结束日期: 2024-12-31
🎯 模型参数:
  - 因子数量: 3
  - 因子阶数: 1
  - 特异性自回归阶数: 1
  - EM最大迭代次数: 50
🔧 优化参数:
  - 变量选择方法: global_backward
  - 信息准则方法: bic
  - 累积方差阈值: 0.8
  - 使用Bai-Ng因子选择: True
📊 数据映射:
  - 行业映射对象: 已提供
  - 变量类型映射: 未提供
    行业映射数量: 76
📁 输出设置:
  - 输出基础目录: test_outputs
  - 启用详细分析: True
  - 生成Excel报告: True

=== ⚠️ 关键诊断信息 ===
🔍 _MODULES_AVAILABLE 状态: True
🔍 输入数据形状: (262, 76)
🔍 选择指标列表: ['规模以上工业增加值:当月同比', '中国:日均产量:生铁:重点企业', '中国:日均产量:粗钢:重点企业', '重点电厂:日耗量:煤炭', '中国:主要45港口:日均疏港量:铁矿石']
🔍 模块导入状态:
    - dfm_core: ✅ 成功
    - DynamicFactorModel: ✅ 成功
    - analysis_utils: ✅ 成功
    - variable_selection: ✅ 成功
    - results_analysis: ✅ 成功
步骤1: 数据准备...
使用传入的input_df进行训练
🔍🔍🔍 关键变量选择诊断:
    - 原始selected_indicators长度: 5
    - 目标变量: 工业增加值同比增速_月度_同花顺
    - 最终available_cols长度: 6
    - available_cols内容: ['工业增加值同比增速_月度_同花顺', '规模以上工业增加值:当月同比', '中国:日均产量:生铁:重点企业', '中国:日均产量:粗钢:重点企业', '重点电厂:日耗量:煤炭', '中国:主要45港口:日均疏港量:铁矿石']
数据子集形状: (262, 5)
时间范围: 2019-12-27 00:00:00 到 2024-12-27 00:00:00
时间过滤后数据形状: (261, 5)
自动计算训练结束日期: 2023-12-29 00:00:00
步骤2: 数据标准化...
步骤3: 因子选择...
🔥 选择的因子数量: 3
步骤4: 跳过变量选择（未启用或模块不可用）
步骤5: 训练最终DFM模型...
DFM模型训练完成
步骤6: 保存结果...
🔍 结果保存目录: test_outputs
🔍 目录是否存在: True
🔍 DFM结果是否存在: True
🔍 开始保存过程...
🔍 尝试保存模型到: test_outputs\final_dfm_model.joblib
🔍 保存真实DFM模型...
✅ 模型保存成功: test_outputs\final_dfm_model.joblib
    文件大小: 29211 bytes
🔍 尝试保存元数据到: test_outputs\final_dfm_metadata.pkl
✅ 元数据保存成功: test_outputs\final_dfm_metadata.pkl
    文件大小: 800 bytes
🔍 尝试保存训练数据到: test_outputs\training_data.csv
✅ 训练数据保存成功: test_outputs\training_data.csv
    文件大小: 19260 bytes
    数据形状: (261, 5)
步骤7: 生成Excel报告...
🔍 尝试生成Excel报告到: test_outputs\comprehensive_dfm_report.xlsx
✅ Excel报告生成成功: test_outputs\comprehensive_dfm_report.xlsx
    文件大小: 8005 bytes
=== ✅ DFM训练管道完成 ===
生成文件数量: 4
🔍🔍🔍 详细文件列表:
    📁 final_model_joblib: test_outputs\final_dfm_model.joblib
       - 存在: ✅
       - 大小: 29211 bytes
    📁 metadata: test_outputs\final_dfm_metadata.pkl
       - 存在: ✅
       - 大小: 800 bytes
    📁 training_data: test_outputs\training_data.csv
       - 存在: ✅
       - 大小: 19260 bytes
    📁 excel_report: test_outputs\comprehensive_dfm_report.xlsx
       - 存在: ✅
       - 大小: 8005 bytes

🔍🔍🔥 强制检查结果目录内容:
    实际目录中的文件数量: 4
    📄 comprehensive_dfm_report.xlsx (8005 bytes)
    📄 final_dfm_metadata.pkl (800 bytes)
    📄 final_dfm_model.joblib (29211 bytes)
    📄 training_data.csv (19260 bytes)
🔍🔍🔥 saved_files字典内容检查:
    字典长度: 4
    字典键: ['final_model_joblib', 'metadata', 'training_data', 'excel_report']
    字典值: ['test_outputs\\final_dfm_model.joblib', 'test_outputs\\final_dfm_metadata.pkl', 'test_outputs\\training_data.csv', 'test_outputs\\comprehensive_dfm_report.xlsx']
