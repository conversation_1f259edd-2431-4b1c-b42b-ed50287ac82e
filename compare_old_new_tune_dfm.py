#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比较新老代码的tune_dfm.py脚本计算结果
严格按照老代码配置进行测试，对比所有关键计算结果
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import date
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_environment():
    """设置环境"""
    print("=" * 80)
    print("🔬 新老代码tune_dfm.py脚本结果比较测试")
    print("=" * 80)

    # 添加路径
    current_dir = os.getcwd()
    dashboard_path = os.path.join(current_dir, 'dashboard')
    old_path = os.path.join(current_dir, 'old')
    data_prep_path = os.path.join(current_dir, 'dashboard', 'DFM', 'data_prep')
    train_model_path = os.path.join(current_dir, 'dashboard', 'DFM', 'train_model')

    for path in [current_dir, dashboard_path, old_path, data_prep_path, train_model_path]:
        if path not in sys.path:
            sys.path.insert(0, path)

    print(f"✅ 添加路径: {dashboard_path}")
    print(f"✅ 添加路径: {old_path}")
    print(f"✅ 添加路径: {data_prep_path}")
    print(f"✅ 添加路径: {train_model_path}")

    return current_dir

def prepare_test_data():
    """准备测试数据"""
    print("\n1️⃣ 准备测试数据...")

    # 使用数据准备模块
    from data_preparation import prepare_data_v3
    
    # 数据准备参数（与老代码配置一致）
    data_prep_params = {
        'file_path': 'data/经济数据库0605.xlsx',
        'target_freq': 'W-FRI',
        'target_sheet_name': '工业增加值同比增速_月度_同花顺',
        'target_variable_name': '规模以上工业增加值:当月同比',
        'consecutive_nan_threshold': 10,
        'data_start_date': '2020-01-01',
        'data_end_date': '2024-12-31',
        'reference_sheet_name': '指标体系',
        'reference_column_name': '高频指标'
    }
    
    print("📊 开始数据准备...")
    prepared_data, industry_map = prepare_data_v3(**data_prep_params)
    
    print(f"✅ 数据准备完成!")
    print(f"   数据形状: {prepared_data.shape}")
    print(f"   时间范围: {prepared_data.index.min()} 到 {prepared_data.index.max()}")
    
    return prepared_data, industry_map

def test_old_tune_dfm(prepared_data, industry_map):
    """测试老代码的tune_dfm.py"""
    print("\n2️⃣ 测试老代码tune_dfm.py...")
    
    try:
        # 导入老代码
        import sys
        old_path = os.path.join(os.getcwd(), 'old')
        if old_path not in sys.path:
            sys.path.insert(0, old_path)
            
        # 导入老代码的配置
        import config as old_config
        
        print("📋 老代码配置参数:")
        print(f"   TRAINING_START_DATE: {old_config.TRAINING_START_DATE}")
        print(f"   TRAIN_END_DATE: {old_config.TRAIN_END_DATE}")
        print(f"   VALIDATION_END_DATE: {old_config.VALIDATION_END_DATE}")
        print(f"   FACTOR_SELECTION_METHOD: {old_config.FACTOR_SELECTION_METHOD}")
        print(f"   FIXED_NUMBER_OF_FACTORS: {old_config.FIXED_NUMBER_OF_FACTORS}")
        print(f"   N_ITER_FIXED: {old_config.N_ITER_FIXED}")
        print(f"   MAX_WORKERS: {old_config.MAX_WORKERS}")
        
        # 准备老代码的参数
        target_var = '规模以上工业增加值:当月同比'
        selected_indicators = [col for col in prepared_data.columns if col != target_var]
        
        old_params = {
            'input_df': prepared_data,
            'target_variable': target_var,
            'selected_indicators': selected_indicators,
            'training_start_date': pd.to_datetime(old_config.TRAINING_START_DATE).date(),
            'training_end_date': pd.to_datetime(old_config.TRAIN_END_DATE).date(),
            'validation_start_date': pd.to_datetime(old_config.TRAIN_END_DATE).date() + pd.Timedelta(days=7),
            'validation_end_date': pd.to_datetime(old_config.VALIDATION_END_DATE).date(),
            'n_factors': old_config.FIXED_NUMBER_OF_FACTORS,
            'em_max_iter': old_config.N_ITER_FIXED,
            'output_base_dir': "test_outputs_old",
            'var_industry_map': industry_map,
            'enable_hyperparameter_tuning': True,
            'enable_variable_selection': True,
            'enable_detailed_analysis': False,
            'generate_excel_report': False
        }
        
        print(f"\n🚀 开始老代码测试...")
        print(f"   参数数量: {len(old_params)}")
        
        # 注意：这里我们只是研究老代码，不实际调用
        print("⚠️ 注意：按照要求，我们只研究老代码，不实际调用")
        
        # 分析老代码的关键函数
        old_results = {
            'status': 'analyzed_only',
            'config': {
                'training_start': old_config.TRAINING_START_DATE,
                'training_end': old_config.TRAIN_END_DATE,
                'validation_end': old_config.VALIDATION_END_DATE,
                'factor_selection_method': old_config.FACTOR_SELECTION_METHOD,
                'fixed_factors': old_config.FIXED_NUMBER_OF_FACTORS,
                'n_iter': old_config.N_ITER_FIXED,
                'max_workers': old_config.MAX_WORKERS
            }
        }
        
        return old_results
        
    except Exception as e:
        print(f"❌ 老代码测试失败: {e}")
        return None

def test_new_tune_dfm(prepared_data, industry_map, old_config_ref):
    """测试新代码的tune_dfm.py"""
    print("\n3️⃣ 测试新代码tune_dfm.py...")
    
    try:
        # 导入新代码
        from tune_dfm import train_and_save_dfm_results
        
        target_var = '规模以上工业增加值:当月同比'
        selected_indicators = [col for col in prepared_data.columns if col != target_var]
        
        # 使用与老代码完全一致的参数
        new_params = {
            'input_df': prepared_data,
            'target_variable': target_var,
            'selected_indicators': selected_indicators,
            'training_start_date': date(2020, 1, 1),      # 与老代码一致
            'training_end_date': date(2024, 6, 28),       # 与老代码一致
            'validation_start_date': date(2024, 7, 5),    # 训练结束后开始验证
            'validation_end_date': date(2024, 12, 27),    # 与老代码一致
            'use_bai_ng_factor_selection': True,          # 与老代码一致
            'info_criterion_method': 'bic',
            'k_factors_range': (1, 20),
            'em_max_iter': 30,                            # 与老代码一致
            'output_base_dir': "test_outputs_new",
            'var_industry_map': industry_map,
            'enable_hyperparameter_tuning': True,
            'enable_variable_selection': True,
            'variable_selection_method': 'global_backward',
            'enable_detailed_analysis': False,            # 简化输出便于比较
            'generate_excel_report': False
        }
        
        print(f"🚀 开始新代码测试...")
        print(f"   参数数量: {len(new_params)}")
        print(f"   数据形状: {prepared_data.shape}")
        
        # 执行新代码
        new_results = train_and_save_dfm_results(**new_params)
        
        print(f"✅ 新代码测试完成!")
        return new_results
        
    except Exception as e:
        print(f"❌ 新代码测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def compare_results(old_results, new_results):
    """比较新老代码结果"""
    print("\n4️⃣ 比较新老代码结果...")
    
    if old_results is None or new_results is None:
        print("❌ 无法比较：缺少测试结果")
        return
    
    print("📊 结果比较:")
    print(f"   老代码状态: {old_results.get('status', 'unknown')}")
    print(f"   新代码状态: {'success' if new_results else 'failed'}")
    
    if old_results.get('config'):
        print("\n📋 配置参数比较:")
        old_config = old_results['config']
        for key, value in old_config.items():
            print(f"   {key}: {value}")
    
    if new_results:
        print(f"\n🔍 新代码关键结果:")
        for key, value in new_results.items():
            if isinstance(value, (int, float, str)):
                print(f"   {key}: {value}")
            elif isinstance(value, dict):
                print(f"   {key}: {type(value)} (字典)")
            elif hasattr(value, 'shape'):
                print(f"   {key}: shape {value.shape}")
            else:
                print(f"   {key}: {type(value)}")

def main():
    """主函数"""
    try:
        # 1. 环境设置
        current_dir = setup_environment()
        
        # 2. 准备数据
        prepared_data, industry_map = prepare_test_data()
        
        # 3. 测试老代码（仅分析）
        old_results = test_old_tune_dfm(prepared_data, industry_map)
        
        # 4. 测试新代码
        new_results = test_new_tune_dfm(prepared_data, industry_map, old_results)
        
        # 5. 比较结果
        compare_results(old_results, new_results)
        
        print("\n" + "=" * 80)
        print("🎯 比较测试完成!")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
