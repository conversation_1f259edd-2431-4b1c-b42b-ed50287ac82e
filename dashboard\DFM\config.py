# -*- coding: utf-8 -*-
"""
DFM模块统一配置文件
定义所有输出路径和目录设置，以及所有默认参数值
"""

import os
from datetime import datetime
import shutil

# 🚀🚀🚀 超级性能优化：环境变量设置 🚀🚀🚀
# 在导入任何科学计算库之前设置环境变量，确保最大化CPU利用率
os.environ['OMP_NUM_THREADS'] = '1'           # OpenMP单线程
os.environ['OPENBLAS_NUM_THREADS'] = '1'      # OpenBLAS单线程
os.environ['MKL_NUM_THREADS'] = '1'           # Intel MKL单线程
os.environ['VECLIB_MAXIMUM_THREADS'] = '1'    # Apple Accelerate单线程
os.environ['NUMEXPR_MAX_THREADS'] = '15'      # NumExpr最大线程数
os.environ['NUMBA_NUM_THREADS'] = '15'        # Numba最大线程数

# 🚀 内存和性能优化
os.environ['PYTHONHASHSEED'] = '42'           # 固定哈希种子，提高可重复性
os.environ['MALLOC_TRIM_THRESHOLD_'] = '0'    # 禁用内存修剪，提高速度
os.environ['MALLOC_MMAP_THRESHOLD_'] = '131072'  # 优化内存分配

# 🚀 NumPy/SciPy性能优化
os.environ['NPY_NUM_BUILD_JOBS'] = '15'       # NumPy构建作业数
os.environ['SCIPY_NUM_BUILD_JOBS'] = '15'     # SciPy构建作业数

print("🚀🚀🚀 终极性能模式已启用：CPU利用率目标99%，20个超级并发进程 🚀🚀🚀")
print("🔥🔥🔥 警告：已启用终极激进模式，将榨干所有CPU资源！🔥🔥🔥")

# 🚨🚨🚨 终极激进环境变量：强制榨干CPU 🚨🚨🚨
os.environ.update({
    # 🔥🔥🔥 线程控制：强制单线程BLAS，避免竞争 🔥🔥🔥
    'OMP_NUM_THREADS': '1',
    'OPENBLAS_NUM_THREADS': '1', 
    'MKL_NUM_THREADS': '1',
    'VECLIB_MAXIMUM_THREADS': '1',
    'NUMEXPR_MAX_THREADS': '20',
    
    # 🔥🔥🔥 Python优化：最大化性能 🔥🔥🔥
    'PYTHONHASHSEED': '0',
    'PYTHONOPTIMIZE': '2',           # 🚀🚀 最高级别优化
    'PYTHONDONTWRITEBYTECODE': '1',  # 🚀 不写字节码，减少I/O
    
    # 🔥🔥🔥 NumPy/SciPy优化：强制高性能 🔥🔥🔥
    'NPY_NUM_BUILD_JOBS': '20',      # 🚀🚀 NumPy构建作业数
    'SCIPY_NUM_BUILD_JOBS': '20',    # 🚀🚀 SciPy构建作业数
    
    # 🔥🔥🔥 内存优化：最大化内存使用 🔥🔥🔥
    'MALLOC_ARENA_MAX': '1',         # 🚀 减少内存碎片
    'MALLOC_MMAP_THRESHOLD_': '131072',  # 🚀 内存映射阈值
    
    # 🔥🔥🔥 系统级优化：强制高性能模式 🔥🔥🔥
    'OMP_WAIT_POLICY': 'ACTIVE',     # 🚀🚀 活跃等待策略
    'OMP_DYNAMIC': 'FALSE',          # 🚀 禁用动态线程调整
    'OMP_PROC_BIND': 'TRUE',         # 🚀🚀 绑定处理器
    'OMP_PLACES': 'cores',           # 🚀 绑定到核心
    
    # 🔥🔥🔥 并发控制：最大化并发度 🔥🔥🔥
    'JOBLIB_MULTIPROCESSING': '1',   # 🚀 启用joblib多进程
    'NUMBA_NUM_THREADS': '20',       # 🚀🚀 Numba线程数
    'NUMBA_THREADING_LAYER': 'workqueue',  # 🚀 Numba线程层
    
    # 🔥🔥🔥 I/O优化：减少I/O延迟 🔥🔥🔥
    'TMPDIR': 'C:\\temp',            # 🚀 使用SSD临时目录
    'TMP': 'C:\\temp',
    'TEMP': 'C:\\temp',
})

# 项目根目录配置
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# === 数据相关默认配置 ===
class DataDefaults:
    """数据处理相关的默认配置"""
    # Excel工作表和列名
    TYPE_MAPPING_SHEET = '指标体系'
    TARGET_VARIABLE = '规模以上工业增加值:当月同比'
    INDICATOR_COLUMN = '高频指标'
    INDUSTRY_COLUMN = '行业'
    TYPE_COLUMN = '类型'
    
    # 数据处理参数
    ADF_P_THRESHOLD = 0.05  # ADF平稳性检验p值阈值
    CONSECUTIVE_NAN_THRESHOLD = None  # 连续缺失值阈值
    
    # 数据频率检测
    FREQ_DAILY = 'D'
    FREQ_WEEKLY = 'W'
    FREQ_MONTHLY = 'M'
    FREQ_QUARTERLY = 'Q'
    FREQ_INFER = 'infer'

# === 训练模型默认配置 ===
class TrainDefaults:
    """模型训练相关的默认配置"""
    # 基础DFM参数
    FACTOR_ORDER = 1
    IDIO_AR_ORDER = 1
    EM_MAX_ITER = 30
    
    # 🚀 变量筛选优化策略：先筛选变量，再选择因子数
    VARIABLE_SELECTION_FIXED_FACTORS = 10  # 变量筛选阶段使用的固定因子数
    
    # 因子选择策略
    FACTOR_SELECTION_STRATEGY = 'information_criteria'
    VARIABLE_SELECTION_METHOD = 'global_backward'
    INFO_CRITERION_METHOD = 'bic'
    
    # 因子选择参数
    IC_MAX_FACTORS = 30  # 🔥 修复：提高到30，确保能选择到9个因子
    K_FACTORS_RANGE_MIN = 1
    K_FACTORS_RANGE_MAX = 30  # 🔥 修复：最终因子数评估范围：1-30
    FIXED_NUMBER_OF_FACTORS = 3  # 固定因子数量策略使用的默认值
    CUM_VARIANCE_THRESHOLD = 0.8
    
    # 超参数调优参数
    ENABLE_HYPERPARAMETER_TUNING = False  # 🔥 禁用超参数搜索，使用Bai-Ng方法
    USE_BAI_NG_FACTOR_SELECTION = True    # 🔥 启用Bai-Ng，与老代码保持一致
    ENABLE_VARIABLE_SELECTION = True
    ENABLE_DETAILED_ANALYSIS = True
    GENERATE_EXCEL_REPORT = True
    
    # 🚨🚨🚨 终极激进性能配置：强制95%+ CPU利用率 🚨🚨🚨
    # 用户强烈要求：CPU利用率必须达到90%+，使用最激进的性能设置
    _CPU_CORES = os.cpu_count() or 8
    _LOGICAL_PROCESSORS = 16  # i7-1260P的逻辑处理器数量
    
    # 🔥🔥🔥🔥 终极激进配置：榨干所有CPU资源 🔥🔥🔥🔥
    MAX_WORKERS = 30              # 🚀🚀🚀 终极并发：30个进程，强制榨干CPU
    OPENBLAS_THREADS = 1          # 🚀🚀 单线程BLAS，避免线程竞争
    TARGET_CPU_USAGE = 0.99       # 🚀🚀🚀 目标99% CPU利用率（终极性能）
    
    # 🔥🔥🔥 终极性能优化参数 🔥🔥🔥
    NUMPY_THREADS = 1             # 🚀 NumPy单线程，避免竞争
    MKL_THREADS = 1               # 🚀 Intel MKL单线程
    NUMEXPR_MAX_THREADS = 20      # 🚀🚀 NumExpr超级线程数
    
    # 🚀🚀🚀 变量选择终极并行配置 🚀🚀🚀
    VARIABLE_SELECTION_USE_PROCESS_POOL = True     # 🔥 启用进程池
    VARIABLE_SELECTION_MAX_WORKERS = 30            # 🔥🔥 变量选择终极并发数
    VARIABLE_SELECTION_CHUNK_SIZE = 1              # 🔥 最小块大小，最大化并行度
    VARIABLE_SELECTION_TIMEOUT = 600               # 🔥 10分钟超时
    VARIABLE_SELECTION_AGGRESSIVE_MODE = True      # 🔥🔥 激进模式
    
    # 🚀🚀🚀 Bai-Ng因子选择终极并行配置 🚀🚀🚀
    BAI_NG_USE_PARALLEL = True                     # 🔥 启用Bai-Ng并行计算
    BAI_NG_MAX_WORKERS = 30                        # 🔥🔥 Bai-Ng终极并发数
    BAI_NG_BATCH_SIZE = 2                          # 🔥 更小批处理，更多并发
    BAI_NG_AGGRESSIVE_PARALLEL = True              # 🔥🔥 Bai-Ng激进并行模式
    
    # 🚀🚀🚀 EM算法终极并行配置 🚀🚀🚀
    EM_USE_PARALLEL = True                         # 🔥 启用EM算法并行
    EM_MAX_WORKERS = 12                            # 🔥🔥 EM算法高并发数
    EM_AGGRESSIVE_MODE = True                      # 🔥🔥 EM激进模式
    
    # 🚀🚀🚀 数据处理终极并行配置 🚀🚀🚀
    DATA_PREP_MAX_WORKERS = 16                     # 🔥🔥 数据预处理并发数
    DATA_PREP_USE_MULTIPROCESSING = True           # 🔥 启用多进程数据处理
    STATIONARITY_CHECK_PARALLEL = True             # 🔥 平稳性检查并行
    STATIONARITY_MAX_WORKERS = 16                  # 🔥 平稳性检查并发数
    
    # 🚀🚀🚀 评估和分析终极并行配置 🚀🚀🚀
    EVALUATION_MAX_WORKERS = 16                    # 🔥🔥 模型评估并发数
    ANALYSIS_MAX_WORKERS = 16                      # 🔥🔥 结果分析并发数
    R2_CALCULATION_PARALLEL = True                 # 🔥 R2计算并行
    R2_MAX_WORKERS = 16                            # 🔥 R2计算并发数
    
    # 🚀🚀🚀 内存和I/O终极优化 🚀🚀🚀
    MEMORY_EFFICIENT_MODE = False                  # 🔥 关闭内存节约模式，优先速度
    CACHE_INTERMEDIATE_RESULTS = True              # 🔥 缓存中间结果
    PRELOAD_DATA = True                            # 🔥 预加载数据到内存
    AGGRESSIVE_CACHING = True                      # 🔥🔥 激进缓存策略
    DISABLE_GC_DURING_COMPUTATION = True           # 🔥 计算时禁用垃圾回收
    
    # 🚀🚀🚀 系统级优化 🚀🚀🚀
    FORCE_HIGH_PRIORITY = True                     # 🔥🔥 强制高优先级
    DISABLE_SWAP = True                            # 🔥 禁用交换文件
    CPU_AFFINITY_ENABLED = True                    # 🔥 启用CPU亲和性
    NUMA_OPTIMIZATION = True                       # 🔥 NUMA优化
    
    # 🚨🚨🚨 原配置逻辑已完全禁用：使用上面的终极激进配置 🚨🚨🚨
    # 所有条件分支都已禁用，确保使用上面定义的终极激进配置
    # MAX_WORKERS = 20, TARGET_CPU_USAGE = 0.99, 等等...
    
    VALIDATION_SPLIT_RATIO = 0.8  # 用于自动分割验证期，作为用户未指定日期时的后备机制
    
    # PCA分析参数
    PCA_N_COMPONENTS = 10
    
    # 默认日期设置 - 两套机制服务于不同场景
    TRAINING_YEARS_BACK = 5  # 动态计算训练开始日期：today - 5年（用于UI初始化和重置）
    # 固定验证期日期：确保模型训练的一致性和可重复性（用于生产环境）
    VALIDATION_END_YEAR = 2024
    VALIDATION_END_MONTH = 12
    VALIDATION_END_DAY = 31
    VALIDATION_START_YEAR = 2024
    VALIDATION_START_MONTH = 7
    VALIDATION_START_DAY = 1
    
    # 训练状态
    STATUS_WAITING = '等待开始'
    STATUS_PREPARING = '准备启动训练...'
    STATUS_TRAINING = '正在训练...'
    STATUS_COMPLETED = '训练完成'
    STATUS_FAILED_PREFIX = '训练失败'

# === UI界面默认配置 ===
class UIDefaults:
    """UI界面相关的默认配置"""
    # 变量选择方法选项
    VARIABLE_SELECTION_OPTIONS = {
        'none': "无筛选 (使用全部变量)",
        'global_backward': "全局后向剔除"
    }
    
    # 因子选择策略选项
    FACTOR_SELECTION_STRATEGY_OPTIONS = {
        'information_criteria': "信息准则 (Information Criteria)",
        'fixed_number': "固定因子数量 (Fixed Number of Factors)",
        'cumulative_variance': "累积共同方差 (Cumulative Common Variance)"
    }
    
    # 信息准则选项
    INFO_CRITERION_OPTIONS = {
        'bic': "BIC (Bayesian Information Criterion)",
        'aic': "AIC (Akaike Information Criterion)"
    }
    
    # UI组件默认值
    MAX_ITERATIONS_DEFAULT = 30
    MAX_ITERATIONS_MIN = 1
    MAX_ITERATIONS_STEP = 10
    
    FIXED_FACTORS_DEFAULT = 3
    FIXED_FACTORS_MIN = 1
    FIXED_FACTORS_STEP = 1
    
    IC_MAX_FACTORS_DEFAULT = 20
    IC_MAX_FACTORS_MIN = 1
    IC_MAX_FACTORS_STEP = 1
    
    CUM_VARIANCE_MIN = 0.1
    CUM_VARIANCE_MAX = 1.0
    CUM_VARIANCE_DEFAULT = 0.8
    CUM_VARIANCE_STEP = 0.05
    
    # 界面布局参数
    NUM_COLS_INDUSTRY = 3  # 行业选择列数
    NUM_COLS_DOWNLOAD = 3  # 下载按钮列数
    LOG_DISPLAY_LINES = 5  # 日志显示行数
    LOG_DISPLAY_HEIGHT = 120  # 日志显示高度

# === 可视化默认配置 ===
class VisualizationDefaults:
    """可视化相关的默认配置"""
    # 图表尺寸
    HEATMAP_HEIGHT_MIN = 600
    HEATMAP_HEIGHT_FACTOR = 35
    HEATMAP_HEIGHT_OFFSET = 200
    HEATMAP_WIDTH_MIN = 1000
    HEATMAP_WIDTH_FACTOR = 100
    HEATMAP_WIDTH_OFFSET = 50
    
    # 因子时间序列图布局
    FACTOR_PLOT_HEIGHT = 400
    FACTOR_PLOT_COLS_EVEN = 2  # 偶数个因子时每行列数
    FACTOR_PLOT_COLS_ODD = 3   # 奇数个因子时每行列数
    
    # 热力图聚类参数
    ENABLE_CLUSTERING = True
    MIN_VARS_FOR_CLUSTERING = 1
    
    # 图表标题和标签
    FACTOR_EVOLUTION_TITLE = "因子时间序列演变图"
    FACTOR_LOADINGS_TITLE = "因子载荷矩阵 (Lambda)"
    HEATMAP_TITLE_SUFFIX = " (变量聚类排序)"

# === 文件处理默认配置 ===
class FileDefaults:
    """文件处理相关的默认配置"""
    # MIME类型
    MIME_PICKLE = "application/octet-stream"
    MIME_JOBLIB = "application/octet-stream"
    MIME_EXCEL = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    MIME_CSV = "text/csv"
    
    # 文件编码
    CSV_ENCODING = 'utf-8-sig'  # Excel兼容的UTF-8编码
    
    # 缓存设置
    CACHE_TTL_HOURS = 1  # 缓存时间1小时
    CACHE_TTL_SECONDS = 3600
    
    # 文件名配置
    REPORT_FILENAME = 'comprehensive_dfm_report.xlsx'
    MODEL_FILENAME = 'final_dfm_model.joblib'
    METADATA_FILENAME = 'final_dfm_metadata.pkl'
    
    # 数据文件名
    TRAINING_DATA_FILENAME = 'training_data.csv'
    EVOLUTION_DATA_FILENAME = 'nowcast_evolution_data_T.csv'
    DECOMPOSITION_DATA_FILENAME = 'news_decomposition_grouped.csv'
    
    # 图表文件名
    EVOLUTION_HTML_FILENAME = 'news_analysis_plot_backend_evo.html'
    DECOMPOSITION_HTML_FILENAME = 'news_analysis_plot_backend_decomp.html'
    BACKEND_PLOT_FILENAME = 'news_analysis_plot_backend.png'
    
    # 支持的文件扩展名
    EXCEL_EXTENSIONS = ['.xls', '.xlsx']
    IMAGE_EXTENSIONS = ['.png', '.jpg', '.jpeg']
    DATA_EXTENSIONS = ['.csv', '.txt']

# === 新闻分析默认配置 ===
class NewsAnalysisDefaults:
    """新闻分析相关的默认配置"""
    # 默认频率
    DEFAULT_MODEL_FREQUENCY = 'M'  # 月度
    
    # 图表文件名
    EVOLUTION_HTML_FILE = 'news_analysis_plot_backend_evo.html'
    DECOMPOSITION_HTML_FILE = 'news_analysis_plot_backend_decomp.html'
    EVOLUTION_DATA_FILE = 'nowcast_evolution_data_T.csv'
    DECOMPOSITION_DATA_FILE = 'news_decomposition_grouped.csv'
    BACKEND_PLOT_FILE = 'news_analysis_plot_backend.png'

# === 数值格式化默认配置 ===
class FormatDefaults:
    """数值格式化相关的默认配置"""
    # 精度设置
    PRECISION_DEFAULT = 2
    PRECISION_HIGH = 4
    PRECISION_PERCENTAGE = 2
    
    # 格式字符串
    NUMBER_FORMAT_DEFAULT = '0.0000'
    PERCENTAGE_FORMAT = '.2f'
    
    # 缺失值显示
    NA_REPRESENTATION = 'N/A'

# === 分析计算默认配置 ===
class AnalysisDefaults:
    """分析计算相关的默认配置"""
    # 超时设置
    TIMEOUT_SECONDS = 120  # R2计算等分析的超时时间
    
    # R2计算参数
    R2_MIN_VARIANCE = 0  # 最小方差阈值
    
    # 指标计算参数
    METRIC_PRECISION = 4
    METRIC_PCT_PRECISION = 2
    
    # 聚类参数
    LINKAGE_METHOD = 'ward'
    LINKAGE_METRIC = 'euclidean'

# === 算法核心参数配置 ===
class AlgorithmDefaults:
    """算法核心计算相关的默认配置"""
    # 随机种子
    RANDOM_SEED = 42
    
    # EM算法初始化参数
    EM_FACTOR_INIT_SCALE = 0.1  # Lambda初始化缩放因子
    EM_AR_COEF_INIT = 0.95      # A矩阵对角线初始值
    EM_Q_INIT = 0.1             # Q矩阵初始值
    EM_R_INIT = 0.1             # R矩阵初始值
    EM_B_INIT = 0.1             # B矩阵初始值
    
    # 数值计算保护
    ZERO_PROTECTION = 1.0       # 除零保护值
    EIGENVALUE_STABILITY_THRESHOLD = 1.0  # 特征值稳定性阈值
    
    # Kalman滤波参数
    KALMAN_AR_FALLBACK = 0.95   # AR系数后备值

# === 性能和并发配置 ===
class PerformanceDefaults:
    """性能优化和并发处理相关的默认配置"""
    # 超时设置
    TIMEOUT_SHORT = 60          # 短期操作超时(秒)
    TIMEOUT_MEDIUM = 120        # 中期操作超时(秒) 
    TIMEOUT_LONG = 600          # 长期操作超时(秒)
    
    # 并发控制
    ANALYSIS_WORKERS = 16       # 分析计算工作进程数
    
    # 批处理配置
    BATCH_SIZE_SMALL = 1        # 小批次处理
    BATCH_SIZE_MEDIUM = 2       # 中批次处理
    BATCH_SIZE_LARGE = 5        # 大批次处理

# === 可视化绘图配置 ===
class PlotDefaults:
    """图表绘制相关的默认配置"""
    # 图表尺寸配置
    FIGURE_SIZE_SMALL = (10, 6)     # 小图尺寸
    FIGURE_SIZE_MEDIUM = (14, 7)    # 中图尺寸
    FIGURE_SIZE_LARGE = (15, 10)    # 大图尺寸
    FIGURE_SIZE_COMPARISON = (15, 25)  # 对比图尺寸
    FIGURE_SIZE_HEATMAP = (12, 10)  # 热力图尺寸
    
    # 子图布局
    SUBPLOT_HEIGHT_PER_ROW = 4.5    # 每行子图高度
    SUBPLOT_WIDTH_PER_COL = 6       # 每列子图宽度
    
    # 行业因子图配置
    INDUSTRY_FACTOR_SUBPLOT_SIZE = (6, 4.5)  # 行业因子子图尺寸
    
    # 聚类热力图配置
    CLUSTERMAP_FIGSIZE = (12, 10)    # 聚类热力图尺寸
    
    # 载荷对比图配置  
    LOADING_COMPARISON_FIGSIZE = (15, 25)    # 载荷对比图尺寸
    LOADING_COMPARISON_THRESHOLD = 0.1       # 载荷对比阈值
    
    # 透明度配置
    ALPHA_MAIN_LINE = 0.8          # 主线条透明度
    ALPHA_BACKGROUND = 0.2         # 背景区域透明度
    ALPHA_GRID = 0.6               # 网格透明度
    ALPHA_GRID_LIGHT = 0.5         # 浅网格透明度
    ALPHA_SECONDARY = 0.7          # 辅助线条透明度
    
    # 线条配置
    LINEWIDTH_MAIN = 1.0           # 主线条宽度
    LINEWIDTH_SECONDARY = 0.8      # 辅助线条宽度
    
    # 布局配置
    TITLE_Y_POSITION = 1.02        # 标题Y位置
    TITLE_Y_POSITION_HIGH = 1.03   # 高标题Y位置
    LEGEND_Y_POSITION = -0.2       # 图例Y位置
    LEGEND_Y_POSITION_LOW = -0.4   # 低图例Y位置
    LEGEND_X_CENTER = 0.5          # 图例X中心位置
    
    # 颜色配置
    COLOR_MAIN = 'blue'            # 主要颜色
    COLOR_SECONDARY = 'grey'       # 次要颜色
    COLOR_VALIDATION = 'yellow'    # 验证期颜色
    COLOR_WHITE = 'white'          # 白色
    COLOR_BLACK = 'black'          # 黑色
    
    # 网格配置
    GRID_LINESTYLE = '--'          # 网格线样式
    GRID_LINESTYLE_LIGHT = ':'     # 浅网格线样式
    
    # 图表边距配置
    TIGHT_LAYOUT_RECT = [0, 0.03, 1, 0.98]  # 紧密布局矩形
    TIGHT_LAYOUT_RECT_TITLE = [0, 0, 1, 1.0]  # 带标题的紧密布局

# === 数据处理配置 ===
class ProcessingDefaults:
    """数据处理相关的默认配置"""
    # 数据分割比例
    TRAIN_SPLIT_RATIO = 0.8        # 训练验证分割比例
    
    # 缺失值处理
    HIGH_MISSING_THRESHOLD = 0.5   # 高缺失率阈值
    
    # 数值格式配置
    SCORE_DEFAULT_VALUE = (0.0, -1.0)  # 默认评分元组
    
    # PCA动态高度计算
    PCA_HEIGHT_SCALE = 0.3         # PCA图表高度缩放因子
    PCA_HEIGHT_MIN = 6             # PCA图表最小高度
    PCA_HEIGHT_MAX = 15            # PCA图表最大高度

# === 文件处理默认配置 ===
class FileDefaults:
    """文件处理相关的默认配置"""
    # MIME类型
    MIME_PICKLE = "application/octet-stream"
    MIME_JOBLIB = "application/octet-stream"
    MIME_EXCEL = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    MIME_CSV = "text/csv"
    
    # 文件编码
    CSV_ENCODING = 'utf-8-sig'  # Excel兼容的UTF-8编码
    
    # 缓存设置
    CACHE_TTL_HOURS = 1  # 缓存时间1小时
    CACHE_TTL_SECONDS = 3600
    
    # 文件名配置
    REPORT_FILENAME = 'comprehensive_dfm_report.xlsx'
    MODEL_FILENAME = 'final_dfm_model.joblib'
    METADATA_FILENAME = 'final_dfm_metadata.pkl'
    
    # 数据文件名
    TRAINING_DATA_FILENAME = 'training_data.csv'
    EVOLUTION_DATA_FILENAME = 'nowcast_evolution_data_T.csv'
    DECOMPOSITION_DATA_FILENAME = 'news_decomposition_grouped.csv'
    
    # 图表文件名
    EVOLUTION_HTML_FILENAME = 'news_analysis_plot_backend_evo.html'
    DECOMPOSITION_HTML_FILENAME = 'news_analysis_plot_backend_decomp.html'
    BACKEND_PLOT_FILENAME = 'news_analysis_plot_backend.png'
    
    # 支持的文件扩展名
    EXCEL_EXTENSIONS = ['.xls', '.xlsx']
    IMAGE_EXTENSIONS = ['.png', '.jpg', '.jpeg']
    DATA_EXTENSIONS = ['.csv', '.txt']

# === TrainModelConfig类定义（保持向后兼容） ===
class TrainModelConfig:
    """训练模型的统一配置类（向后兼容）"""
    
    # 项目根目录
    PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    # DFM输出目录
    DFM_TRAIN_OUTPUT_DIR = os.path.join(PROJECT_ROOT, "dashboard", "DFM", "outputs")
    
    # 训练结果文件名
    TRAIN_RESULT_FILES = {
        'model_joblib': FileDefaults.MODEL_FILENAME,
        'metadata': FileDefaults.METADATA_FILENAME,
        'excel_report': FileDefaults.REPORT_FILENAME
    }
    
    # Excel数据文件路径候选
    EXCEL_CANDIDATES = [
        os.path.join(PROJECT_ROOT, "data", "经济数据库0508.xlsx"),
        os.path.join(PROJECT_ROOT, "data", "wind数据", "经济数据库0508.xlsx"),
        os.path.join(PROJECT_ROOT, "dashboard", "经济数据库0508.xlsx")
    ]
    
    @classmethod
    def get_excel_path(cls):
        """获取可用的Excel文件路径"""
        for path in cls.EXCEL_CANDIDATES:
            if os.path.exists(path):
                return path
        return cls.EXCEL_CANDIDATES[0]  # 返回第一个作为默认值

# === 统一输出目录配置 ===
# 所有DFM相关输出都放在dashboard/DFM/outputs下
DFM_BASE_OUTPUT_DIR = os.path.join(PROJECT_ROOT, "dashboard", "DFM", "outputs")

# 各功能模块的子目录 - 直接保存到顶层，不使用train_results中间层
DFM_NEWS_OUTPUT_DIR = os.path.join(DFM_BASE_OUTPUT_DIR, "news_analysis")
DFM_EVOLUTION_OUTPUT_DIR = os.path.join(DFM_BASE_OUTPUT_DIR, "nowcast_evolution") 
DFM_REPORTS_OUTPUT_DIR = os.path.join(DFM_BASE_OUTPUT_DIR, "reports")
DFM_PLOTS_OUTPUT_DIR = os.path.join(DFM_BASE_OUTPUT_DIR, "plots")
DFM_DATA_OUTPUT_DIR = os.path.join(DFM_BASE_OUTPUT_DIR, "data")
DFM_MODELS_OUTPUT_DIR = os.path.join(DFM_BASE_OUTPUT_DIR, "models")

# === 数据准备输出配置 ===
DATA_PREP_OUTPUT_DIR = os.path.join(DFM_BASE_OUTPUT_DIR, "data_prep")

# === 文件命名规范 ===
def get_timestamped_filename(base_name: str, extension: str) -> str:
    """获取带时间戳的文件名"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return f"{base_name}_{timestamp}.{extension}"

def get_timestamped_dir(base_name: str) -> str:
    """获取带时间戳的目录名"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return f"{base_name}_{timestamp}"

# === 新闻分析文件名规范 ===
NEWS_RESULT_FILES = {
    'evolution_html': NewsAnalysisDefaults.EVOLUTION_HTML_FILE,
    'decomposition_html': NewsAnalysisDefaults.DECOMPOSITION_HTML_FILE,
    'evolution_data': NewsAnalysisDefaults.EVOLUTION_DATA_FILE,
    'decomposition_data': NewsAnalysisDefaults.DECOMPOSITION_DATA_FILE,
    'backend_plot': NewsAnalysisDefaults.BACKEND_PLOT_FILE
}

# === 确保输出目录存在 ===
def ensure_output_dirs():
    """确保所有输出目录存在"""
    dirs_to_create = [
        DFM_BASE_OUTPUT_DIR,
        DFM_NEWS_OUTPUT_DIR,
        DFM_EVOLUTION_OUTPUT_DIR,
        DFM_REPORTS_OUTPUT_DIR,
        DFM_PLOTS_OUTPUT_DIR,
        DFM_DATA_OUTPUT_DIR,
        DFM_MODELS_OUTPUT_DIR,
        DATA_PREP_OUTPUT_DIR
    ]
    
    for dir_path in dirs_to_create:
        os.makedirs(dir_path, exist_ok=True)

# === 清理和管理功能 ===
def get_output_summary():
    """获取输出目录汇总信息"""
    ensure_output_dirs()
    
    summary = {}
    dirs_to_check = [
        ('新闻分析', DFM_NEWS_OUTPUT_DIR),
        ('演化分析', DFM_EVOLUTION_OUTPUT_DIR),
        ('报告', DFM_REPORTS_OUTPUT_DIR),
        ('图表', DFM_PLOTS_OUTPUT_DIR),
        ('数据', DFM_DATA_OUTPUT_DIR),
        ('模型', DFM_MODELS_OUTPUT_DIR),
        ('数据准备', DATA_PREP_OUTPUT_DIR)
    ]
    
    for name, dir_path in dirs_to_check:
        if os.path.exists(dir_path):
            files = [f for f in os.listdir(dir_path) if os.path.isfile(os.path.join(dir_path, f))]
            subdirs = [d for d in os.listdir(dir_path) if os.path.isdir(os.path.join(dir_path, d))]
            summary[name] = {
                'path': dir_path,
                'files_count': len(files),
                'subdirs_count': len(subdirs),
                'files': files[:10],  # 只显示前10个文件
                'subdirs': subdirs[:10]  # 只显示前10个子目录
            }
        else:
            summary[name] = {
                'path': dir_path,
                'files_count': 0,
                'subdirs_count': 0,
                'files': [],
                'subdirs': []
            }
    
    return summary

# === 兼容性配置（向后兼容） ===
# 为了保持向后兼容，保留一些旧的配置名称
DEFAULT_OUTPUT_BASE_DIR = DFM_BASE_OUTPUT_DIR
NOWCAST_EVOLUTION_OUTPUT_DIR = DFM_EVOLUTION_OUTPUT_DIR

# === UI默认配置值（向后兼容，使用新的配置类） ===
UI_DEFAULT_TYPE_MAPPING_SHEET = DataDefaults.TYPE_MAPPING_SHEET
UI_DEFAULT_TARGET_VARIABLE = DataDefaults.TARGET_VARIABLE
UI_DEFAULT_INDICATOR_COLUMN = DataDefaults.INDICATOR_COLUMN
UI_DEFAULT_INDUSTRY_COLUMN = DataDefaults.INDUSTRY_COLUMN
UI_DEFAULT_TYPE_COLUMN = DataDefaults.TYPE_COLUMN

# 初始化时确保目录存在
ensure_output_dirs()

# 确保所有配置变量都被导出
__all__ = [
    # 配置类
    'DataDefaults',
    'TrainDefaults', 
    'UIDefaults',
    'VisualizationDefaults',
    'FileDefaults',
    'NewsAnalysisDefaults',
    'FormatDefaults',
    'AnalysisDefaults',
    'AlgorithmDefaults',
    'PerformanceDefaults',
    'PlotDefaults',
    'ProcessingDefaults',
    
    # 向后兼容
    'TrainModelConfig',
    'DFM_TRAIN_OUTPUT_DIR',
    'DFM_DATA_OUTPUT_DIR', 
    'DFM_MODEL_OUTPUT_DIR',
    'DFM_PLOTS_OUTPUT_DIR',
    'DFM_REPORTS_OUTPUT_DIR',
    'DFM_NOWCAST_EVOLUTION_OUTPUT_DIR',
    'ensure_output_dirs',
    
    # UI默认值
    'UI_DEFAULT_TYPE_MAPPING_SHEET',
    'UI_DEFAULT_TARGET_VARIABLE', 
    'UI_DEFAULT_INDICATOR_COLUMN',
    'UI_DEFAULT_INDUSTRY_COLUMN',
    'UI_DEFAULT_TYPE_COLUMN'
]

# 添加默认配置以防万一
if 'DFM_TRAIN_OUTPUT_DIR' not in globals():
    import os
    current_dir = os.path.dirname(__file__)
    outputs_dir = os.path.join(current_dir, 'outputs')
    
    DFM_TRAIN_OUTPUT_DIR = outputs_dir
    DFM_DATA_OUTPUT_DIR = os.path.join(outputs_dir, 'data')
    DFM_MODEL_OUTPUT_DIR = os.path.join(outputs_dir, 'models')
    DFM_PLOTS_OUTPUT_DIR = os.path.join(outputs_dir, 'plots')
    DFM_REPORTS_OUTPUT_DIR = os.path.join(outputs_dir, 'reports')
    DFM_NOWCAST_EVOLUTION_OUTPUT_DIR = os.path.join(outputs_dir, 'nowcast_evolution')

# 🚨🚨🚨 模块级别变量导出：确保配置生效 🚨🚨🚨
# 将TrainDefaults中的终极激进配置导出为模块级别变量

# 🔥🔥🔥🔥 终极激进配置：榨干所有CPU资源 🔥🔥🔥🔥
MAX_WORKERS = TrainDefaults.MAX_WORKERS                                    # 🚀🚀🚀 超级并发：20个进程
TARGET_CPU_USAGE = TrainDefaults.TARGET_CPU_USAGE                          # 🚀🚀🚀 目标99% CPU利用率
OPENBLAS_THREADS = TrainDefaults.OPENBLAS_THREADS                          # 🚀🚀 单线程BLAS
NUMPY_THREADS = TrainDefaults.NUMPY_THREADS                                # 🚀 NumPy单线程
MKL_THREADS = TrainDefaults.MKL_THREADS                                    # 🚀 Intel MKL单线程
NUMEXPR_MAX_THREADS = TrainDefaults.NUMEXPR_MAX_THREADS                    # 🚀🚀 NumExpr超级线程数

# 🚀🚀🚀 变量选择终极并行配置 🚀🚀🚀
VARIABLE_SELECTION_USE_PROCESS_POOL = TrainDefaults.VARIABLE_SELECTION_USE_PROCESS_POOL     # 🔥 启用进程池
VARIABLE_SELECTION_MAX_WORKERS = TrainDefaults.VARIABLE_SELECTION_MAX_WORKERS               # 🔥🔥 变量选择超级并发数：20
VARIABLE_SELECTION_CHUNK_SIZE = TrainDefaults.VARIABLE_SELECTION_CHUNK_SIZE                 # 🔥 最小块大小
VARIABLE_SELECTION_TIMEOUT = TrainDefaults.VARIABLE_SELECTION_TIMEOUT                       # 🔥 10分钟超时
VARIABLE_SELECTION_AGGRESSIVE_MODE = TrainDefaults.VARIABLE_SELECTION_AGGRESSIVE_MODE       # 🔥🔥 激进模式

# 🚀🚀🚀 Bai-Ng因子选择终极并行配置 🚀🚀🚀
BAI_NG_USE_PARALLEL = TrainDefaults.BAI_NG_USE_PARALLEL                     # 🔥 启用Bai-Ng并行计算
BAI_NG_MAX_WORKERS = TrainDefaults.BAI_NG_MAX_WORKERS                       # 🔥🔥 Bai-Ng超级并发数：20
BAI_NG_BATCH_SIZE = TrainDefaults.BAI_NG_BATCH_SIZE                         # 🔥 更小批处理
BAI_NG_AGGRESSIVE_PARALLEL = TrainDefaults.BAI_NG_AGGRESSIVE_PARALLEL       # 🔥🔥 Bai-Ng激进并行模式

# 🚀🚀🚀 EM算法终极并行配置 🚀🚀🚀
EM_USE_PARALLEL = TrainDefaults.EM_USE_PARALLEL                             # 🔥 启用EM算法并行
EM_MAX_WORKERS = TrainDefaults.EM_MAX_WORKERS                               # 🔥🔥 EM算法高并发数：12
EM_AGGRESSIVE_MODE = TrainDefaults.EM_AGGRESSIVE_MODE                       # 🔥🔥 EM激进模式

# 🚀🚀🚀 数据处理终极并行配置 🚀🚀🚀
DATA_PREP_MAX_WORKERS = TrainDefaults.DATA_PREP_MAX_WORKERS                 # 🔥🔥 数据预处理并发数：16
DATA_PREP_USE_MULTIPROCESSING = TrainDefaults.DATA_PREP_USE_MULTIPROCESSING # 🔥 启用多进程数据处理
STATIONARITY_CHECK_PARALLEL = TrainDefaults.STATIONARITY_CHECK_PARALLEL     # 🔥 平稳性检查并行
STATIONARITY_MAX_WORKERS = TrainDefaults.STATIONARITY_MAX_WORKERS           # 🔥 平稳性检查并发数：16

# 🚀🚀🚀 评估和分析终极并行配置 🚀🚀🚀
EVALUATION_MAX_WORKERS = TrainDefaults.EVALUATION_MAX_WORKERS               # 🔥🔥 模型评估并发数：16
ANALYSIS_MAX_WORKERS = TrainDefaults.ANALYSIS_MAX_WORKERS                   # 🔥🔥 结果分析并发数：16
R2_CALCULATION_PARALLEL = TrainDefaults.R2_CALCULATION_PARALLEL             # 🔥 R2计算并行
R2_MAX_WORKERS = TrainDefaults.R2_MAX_WORKERS                               # 🔥 R2计算并发数：16

# 🚀🚀🚀 内存和I/O终极优化 🚀🚀🚀
MEMORY_EFFICIENT_MODE = TrainDefaults.MEMORY_EFFICIENT_MODE                 # 🔥 关闭内存节约模式
CACHE_INTERMEDIATE_RESULTS = TrainDefaults.CACHE_INTERMEDIATE_RESULTS       # 🔥 缓存中间结果
PRELOAD_DATA = TrainDefaults.PRELOAD_DATA                                   # 🔥 预加载数据到内存
AGGRESSIVE_CACHING = TrainDefaults.AGGRESSIVE_CACHING                       # 🔥🔥 激进缓存策略
DISABLE_GC_DURING_COMPUTATION = TrainDefaults.DISABLE_GC_DURING_COMPUTATION # 🔥 计算时禁用垃圾回收

# 🚀🚀🚀 系统级优化 🚀🚀🚀
FORCE_HIGH_PRIORITY = TrainDefaults.FORCE_HIGH_PRIORITY                     # 🔥🔥 强制高优先级
DISABLE_SWAP = TrainDefaults.DISABLE_SWAP                                   # 🔥 禁用交换文件
CPU_AFFINITY_ENABLED = TrainDefaults.CPU_AFFINITY_ENABLED                   # 🔥 启用CPU亲和性
NUMA_OPTIMIZATION = TrainDefaults.NUMA_OPTIMIZATION                         # 🔥 NUMA优化

print(f"🚀🚀🚀 配置导出完成：MAX_WORKERS={MAX_WORKERS}, TARGET_CPU_USAGE={TARGET_CPU_USAGE}")
print(f"🔥🔥🔥 变量选择并发数：{VARIABLE_SELECTION_MAX_WORKERS}, Bai-Ng并发数：{BAI_NG_MAX_WORKERS}")
print(f"🔥🔥🔥 EM并发数：{EM_MAX_WORKERS}, 数据处理并发数：{DATA_PREP_MAX_WORKERS}")

# 🚨🚨🚨 强制应用环境变量设置 🚨🚨🚨
import multiprocessing
multiprocessing.set_start_method('spawn', force=True)  # 🔥 强制使用spawn方法，确保并行生效

if __name__ == "__main__":
    # 测试配置
    print("DFM模块配置信息:")
    print(f"基础输出目录: {DFM_BASE_OUTPUT_DIR}")
    print(f"新闻分析目录: {DFM_NEWS_OUTPUT_DIR}")
    print(f"演化分析目录: {DFM_EVOLUTION_OUTPUT_DIR}")
    
    print("\n默认配置测试:")
    print(f"默认目标变量: {DataDefaults.TARGET_VARIABLE}")
    print(f"默认因子选择策略: {TrainDefaults.FACTOR_SELECTION_STRATEGY}")
    print(f"默认最大迭代次数: {TrainDefaults.EM_MAX_ITER}")
    print(f"ADF检验阈值: {DataDefaults.ADF_P_THRESHOLD}")
    
    print("\n输出目录汇总:")
    summary = get_output_summary()
    for name, info in summary.items():
        print(f"{name}: {info['files_count']}个文件, {info['subdirs_count']}个子目录") 