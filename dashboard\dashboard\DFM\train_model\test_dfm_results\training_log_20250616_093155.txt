=== 🚀 启动DFM训练管道 ===
目标变量: 规模以上工业增加值:当月同比
选择的指标数量: 0
🔥 因子选择范围: (1, 20)
🔥 启用变量选择: False
🔥 启用超参数调优: True

=== 📋 参数传递验证 ===
📅 时间参数:
  - 训练开始日期: 2020-01-01
  - 训练结束日期: None
  - 验证开始日期: None
  - 验证结束日期: None
🎯 模型参数:
  - 因子数量: 3
  - 因子阶数: 1
  - 特异性自回归阶数: 1
  - EM最大迭代次数: 100
🔧 优化参数:
  - 变量选择方法: global_backward
  - 信息准则方法: bic
  - 累积方差阈值: 0.8
  - 使用Bai-Ng因子选择: True
📊 数据映射:
  - 行业映射对象: 未提供
  - 变量类型映射: 未提供
📁 输出设置:
  - 输出基础目录: dashboard/DFM/train_model/test_dfm_results
  - 启用详细分析: True
  - 生成Excel报告: True

=== ⚠️ 关键诊断信息 ===
🔍 _MODULES_AVAILABLE 状态: True
🔍 输入数据形状: (5, 1)
🔍 选择指标列表: []
🔍 模块导入状态:
    - dfm_core: ✅ 成功
    - DynamicFactorModel: ✅ 成功
    - analysis_utils: ✅ 成功
    - variable_selection: ✅ 成功
    - results_analysis: ✅ 成功
步骤1: 数据准备...
使用传入的input_df进行训练
🔍🔍🔍 关键变量选择诊断:
    - 原始selected_indicators长度: 0
    - 目标变量: 规模以上工业增加值:当月同比
    - 最终available_cols长度: 1
    - available_cols内容: ['规模以上工业增加值:当月同比']
❌❌❌ 错误：未选择任何预测指标！
❌❌❌ DFM模型需要至少一个预测变量才能进行训练！
❌❌❌ 当前数据只包含目标变量，无法进行DFM训练。
💡💡💡 解决方案：
    1. 请确保在数据准备阶段加载了包含多个变量的完整数据集
    2. 在变量选择界面选择至少一个预测指标
    3. 确认Excel文件包含多个工作表的数据
