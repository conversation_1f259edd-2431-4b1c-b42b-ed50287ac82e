#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DFM模型训练测试脚本
仅使用dashboard文件夹中的功能，模拟截图中的参数设置
"""

import os
import sys
import pandas as pd
from datetime import datetime, date
import traceback

# 全局日志文件
LOG_FILE = 'test_execution_log.txt'

def log_and_print(message):
    """同时打印到控制台和记录到日志文件"""
    print(message)
    try:
        with open(LOG_FILE, 'a', encoding='utf-8') as f:
            f.write(f"{datetime.now()}: {message}\n")
    except:
        pass  # 如果日志写入失败，不影响主程序

def init_log():
    """初始化日志文件"""
    try:
        with open(LOG_FILE, 'w', encoding='utf-8') as f:
            f.write(f"DFM测试执行日志 - 开始时间: {datetime.now()}\n")
            f.write("=" * 60 + "\n")
    except:
        pass  # 如果日志初始化失败，不影响主程序

def setup_environment():
    """设置环境"""
    # 添加dashboard路径
    dashboard_path = os.path.join(os.getcwd(), 'dashboard')
    if dashboard_path not in sys.path:
        sys.path.insert(0, dashboard_path)

    log_and_print(f"✅ 添加路径: {dashboard_path}")
    return dashboard_path

def check_data_file():
    """检查数据文件"""
    excel_file = 'data/经济数据库0605.xlsx'
    log_and_print(f"📁 检查数据文件: {excel_file}")

    if not os.path.exists(excel_file):
        log_and_print(f"❌ 数据文件不存在: {excel_file}")
        return None

    file_size = os.path.getsize(excel_file) / 1024 / 1024
    log_and_print(f"✅ 数据文件存在，大小: {file_size:.2f} MB")
    
    # 检查Excel文件结构
    try:
        xl_file = pd.ExcelFile(excel_file)
        sheet_names = xl_file.sheet_names
        log_and_print(f"📊 Excel文件包含 {len(sheet_names)} 个工作表")

        # 检查关键工作表
        target_sheet = '工业增加值同比增速_月度_同花顺'  # 使用实际存在的工作表名
        indicators_sheet = '指标体系'

        if target_sheet in sheet_names:
            log_and_print(f"✅ 找到目标工作表: {target_sheet}")
        else:
            log_and_print(f"❌ 未找到目标工作表: {target_sheet}")
            log_and_print("可用工作表前10个:")
            for i, name in enumerate(sheet_names[:10]):
                log_and_print(f"  {i+1}. {name}")

        if indicators_sheet in sheet_names:
            log_and_print(f"✅ 找到指标体系工作表: {indicators_sheet}")
        else:
            log_and_print(f"❌ 未找到指标体系工作表: {indicators_sheet}")

        return excel_file

    except Exception as e:
        log_and_print(f"❌ 读取Excel文件出错: {e}")
        log_and_print(f"详细错误: {traceback.format_exc()}")
        return None

def test_data_preparation(excel_file):
    """测试数据准备功能"""
    log_and_print(f"\n🔧 测试数据准备模块...")

    try:
        from DFM.data_prep.data_preparation import prepare_data, load_mappings
        log_and_print("✅ 数据准备模块导入成功")
    except ImportError as e:
        log_and_print(f"❌ 数据准备模块导入失败: {e}")
        log_and_print(f"详细错误: {traceback.format_exc()}")
        return None, None, None
    
    # 基于截图的参数设置（使用实际存在的工作表名称）
    params = {
        'target_freq': 'W-FRI',
        'target_sheet_name': '工业增加值同比增速_月度_同花顺',  # 使用实际存在的工作表
        'target_variable_name': '规模以上工业增加值:当月同比',  # 使用实际存在的变量名
        'consecutive_nan_threshold': 10,
        'data_start_date': '2020-01-01',
        'data_end_date': '2024-12-31',
        'reference_sheet_name': '指标体系',
        'reference_column_name': '高频指标'
    }
    
    log_and_print("📊 开始数据准备，参数设置:")
    for key, value in params.items():
        log_and_print(f"  {key}: {value}")

    try:
        # 执行数据准备
        prepared_data, industry_map, transform_log, removed_vars_log = prepare_data(
            excel_path=excel_file,
            target_freq=params['target_freq'],
            target_sheet_name=params['target_sheet_name'],
            target_variable_name=params['target_variable_name'],
            consecutive_nan_threshold=params['consecutive_nan_threshold'],
            data_start_date=params['data_start_date'],
            data_end_date=params['data_end_date'],
            reference_sheet_name=params['reference_sheet_name'],
            reference_column_name=params['reference_column_name']
        )
        
        if prepared_data is not None and not prepared_data.empty:
            log_and_print(f"✅ 数据准备成功!")
            log_and_print(f"   数据形状: {prepared_data.shape}")
            log_and_print(f"   时间范围: {prepared_data.index.min()} 到 {prepared_data.index.max()}")
            log_and_print(f"   变量数量: {len(prepared_data.columns)}")

            # 保存准备好的数据
            prepared_data.to_csv('test_prepared_data.csv', encoding='utf-8-sig')
            log_and_print("✅ 准备好的数据已保存到 test_prepared_data.csv")

            # 显示变量列表
            log_and_print(f"变量列表: {list(prepared_data.columns)}")

            return prepared_data, industry_map, params

        else:
            log_and_print(f"❌ 数据准备失败，返回空数据")
            return None, None, None

    except Exception as e:
        log_and_print(f"❌ 数据准备过程出错: {e}")
        log_and_print(f"详细错误: {traceback.format_exc()}")
        return None, None, None

def test_model_training(prepared_data, industry_map, params):
    """测试模型训练功能"""
    log_and_print(f"\n🤖 测试模型训练模块...")

    try:
        from DFM.train_model.tune_dfm import train_and_save_dfm_results
        log_and_print("✅ 训练模块导入成功")
    except ImportError as e:
        log_and_print(f"❌ 训练模块导入失败: {e}")
        log_and_print(f"详细错误: {traceback.format_exc()}")
        return False
    
    # 选择预测变量
    target_var = params['target_variable_name']
    all_vars = list(prepared_data.columns)
    predictor_vars = [col for col in all_vars if col != target_var]

    log_and_print(f"🎯 变量选择:")
    log_and_print(f"   目标变量: {target_var}")
    log_and_print(f"   可用预测变量数量: {len(predictor_vars)}")

    if len(predictor_vars) == 0:
        log_and_print(f"❌ 没有预测变量，无法进行DFM训练")
        return False

    # 使用全部76个预测变量进行测试
    selected_indicators = predictor_vars  # 使用全部预测变量
    log_and_print(f"   使用全部预测变量数量: {len(selected_indicators)}")
    log_and_print(f"   前10个预测变量: {selected_indicators[:10]}")
    log_and_print(f"   后10个预测变量: {selected_indicators[-10:]}")
    
    # 训练参数（与老代码配置一致）
    training_params = {
        'input_df': prepared_data,
        'target_variable': target_var,
        'selected_indicators': selected_indicators,
        'training_start_date': date(2020, 1, 1),      # 🔥 修复：与老代码一致
        'training_end_date': date(2024, 6, 28),       # 🔥 修复：添加训练结束日期
        'validation_start_date': date(2024, 7, 5),    # 🔥 修复：训练结束后开始验证
        'validation_end_date': date(2024, 12, 27),    # 🔥 修复：与老代码一致
        'n_factors': 3,                               # ✅ 与老代码一致
        'em_max_iter': 30,                            # 🔥 修复：与老代码一致 (N_ITER_FIXED=30)
        'output_base_dir': "test_outputs",
        'var_industry_map': industry_map,
        'enable_hyperparameter_tuning': False,        # 暂时禁用超参数调优
        'enable_variable_selection': True,            # 启用真正的变量选择
        'enable_detailed_analysis': True,
        'generate_excel_report': True
    }
    
    log_and_print(f"🚀 开始模型训练，参数:")
    for key, value in training_params.items():
        if key != 'input_df':  # 不打印大数据对象
            log_and_print(f"   {key}: {value}")

    try:
        # 执行训练
        training_results = train_and_save_dfm_results(**training_params)

        if training_results:
            log_and_print(f"✅ 模型训练成功!")
            log_and_print(f"生成的文件:")
            for file_type, file_path in training_results.items():
                if file_path and os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    log_and_print(f"  ✅ {file_type}: {file_path} ({file_size} bytes)")
                else:
                    log_and_print(f"  ❌ {file_type}: {file_path} (文件不存在)")
            return True
        else:
            log_and_print(f"❌ 模型训练失败，未返回结果")
            return False

    except Exception as e:
        log_and_print(f"❌ 模型训练过程出错: {e}")
        log_and_print(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主测试函数"""
    # 初始化日志
    init_log()

    log_and_print("=" * 60)
    log_and_print("🚀 DFM模型完整测试 - 基于截图参数")
    log_and_print("=" * 60)
    
    # 记录测试结果
    test_results = {
        'environment_setup': False,
        'data_file_check': False,
        'data_preparation': False,
        'model_training': False
    }
    
    try:
        # 1. 环境设置
        log_and_print(f"\n1️⃣ 环境设置...")
        setup_environment()
        test_results['environment_setup'] = True

        # 2. 检查数据文件
        log_and_print(f"\n2️⃣ 检查数据文件...")
        excel_file = check_data_file()
        if excel_file:
            test_results['data_file_check'] = True
        else:
            log_and_print(f"❌ 数据文件检查失败，终止测试")
            return test_results
        
        # 3. 测试数据准备
        log_and_print(f"\n3️⃣ 测试数据准备...")
        prepared_data, industry_map, params = test_data_preparation(excel_file)
        if prepared_data is not None:
            test_results['data_preparation'] = True
        else:
            log_and_print(f"❌ 数据准备失败，终止测试")
            return test_results

        # 4. 测试模型训练
        log_and_print(f"\n4️⃣ 测试模型训练...")
        training_success = test_model_training(prepared_data, industry_map, params)
        if training_success:
            test_results['model_training'] = True

        return test_results

    except Exception as e:
        log_and_print(f"❌ 测试过程出现异常: {e}")
        log_and_print(f"详细错误: {traceback.format_exc()}")
        return test_results

if __name__ == "__main__":
    results = main()

    log_and_print(f"\n" + "=" * 60)
    log_and_print(f"📊 测试结果总结")
    log_and_print(f"=" * 60)

    for test_name, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        log_and_print(f"{test_name}: {status}")

    all_success = all(results.values())
    if all_success:
        log_and_print(f"\n🎉 所有测试都成功完成!")
        log_and_print(f"✅ DFM模型训练流程正常工作")
    else:
        log_and_print(f"\n⚠️ 部分测试失败")
        log_and_print(f"请检查失败的步骤并修复问题")

    # 保存测试结果
    with open('test_results.txt', 'w', encoding='utf-8') as f:
        f.write(f"DFM模型测试结果\n")
        f.write(f"测试时间: {datetime.now()}\n")
        f.write(f"总体结果: {'成功' if all_success else '失败'}\n\n")
        f.write("详细结果:\n")
        for test_name, success in results.items():
            f.write(f"{test_name}: {'成功' if success else '失败'}\n")

    log_and_print(f"\n📄 详细结果已保存到 test_results.txt")
    log_and_print(f"📄 详细执行日志已保存到 {LOG_FILE}")
