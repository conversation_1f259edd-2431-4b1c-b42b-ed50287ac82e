DFM测试执行日志 - 开始时间: 2025-06-16 10:09:19.210564
============================================================
2025-06-16 10:09:19.211885: ============================================================
2025-06-16 10:09:19.212616: 🚀 DFM模型完整测试 - 基于截图参数
2025-06-16 10:09:19.213286: ============================================================
2025-06-16 10:09:19.213998: 
1️⃣ 环境设置...
2025-06-16 10:09:19.214575: ✅ 添加路径: C:\Users\<USER>\Desktop\HFTA0610_test\dashboard
2025-06-16 10:09:19.215225: 
2️⃣ 检查数据文件...
2025-06-16 10:09:19.216174: 📁 检查数据文件: data/经济数据库0605.xlsx
2025-06-16 10:09:19.217004: ✅ 数据文件存在，大小: 0.48 MB
2025-06-16 10:09:19.424719: 📊 Excel文件包含 15 个工作表
2025-06-16 10:09:19.425902: ✅ 找到目标工作表: 工业增加值同比增速_月度_同花顺
2025-06-16 10:09:19.426587: ✅ 找到指标体系工作表: 指标体系
2025-06-16 10:09:19.427362: 
3️⃣ 测试数据准备...
2025-06-16 10:09:19.428294: 
🔧 测试数据准备模块...
2025-06-16 10:09:20.328675: ✅ 数据准备模块导入成功
2025-06-16 10:09:20.329596: 📊 开始数据准备，参数设置:
2025-06-16 10:09:20.330677:   target_freq: W-FRI
2025-06-16 10:09:20.331288:   target_sheet_name: 工业增加值同比增速_月度_同花顺
2025-06-16 10:09:20.331709:   target_variable_name: 工业增加值同比增速_月度_同花顺
2025-06-16 10:09:20.332359:   consecutive_nan_threshold: 10
2025-06-16 10:09:20.333229:   data_start_date: 2020-01-01
2025-06-16 10:09:20.333709:   data_end_date: 2024-12-31
2025-06-16 10:09:20.334089:   reference_sheet_name: 指标体系
2025-06-16 10:09:20.334590:   reference_column_name: 高频指标
2025-06-16 10:09:21.628813: ✅ 数据准备成功!
2025-06-16 10:09:21.629382:    数据形状: (262, 76)
2025-06-16 10:09:21.629776:    时间范围: 2019-12-27 00:00:00 到 2024-12-27 00:00:00
2025-06-16 10:09:21.630063:    变量数量: 76
2025-06-16 10:09:21.643926: ✅ 准备好的数据已保存到 test_prepared_data.csv
2025-06-16 10:09:21.644449: 变量列表: ['规模以上工业增加值:当月同比', '中国:日均产量:生铁:重点企业', '中国:日均产量:粗钢:重点企业', '重点电厂:日耗量:煤炭', '中国:主要45港口:日均疏港量:铁矿石', '中国:生产率:焦炉:国内独立焦化厂(230家)', '中国:高炉开工率(247家)', '中国:产量:螺纹钢:主要钢厂', '中国:开工率:线材:主要钢厂', '中国:产量:线材:主要钢厂', '中国:产量:冷轧板卷:主要钢厂', '中国:产量:热轧板卷:主要钢厂', '中国:产量:中厚板:主要钢厂', '中国:产量:钢材:重点钢铁企业', '中国:开工率:产能(>200万吨):焦化企业(230家)', '中国:开工率:产能(100-200万吨):焦化企业(230家)', '中国:主流港口:库存量:煤炭', '中国:库存量:焦炭:国内样本钢厂(247家)', '动力煤：462家样本矿山：产能利用率（周）', '动力煤：462家样本矿山：日均产量（周）', '精煤：样本洗煤厂（110家）：日均产量：中国（周）', '煤炭：样本洗煤厂（110家）：开工率：中国（周）', '精煤：523家样本矿山：日均产量（周）', '原煤：523家样本矿山：日均产量（周）', '炼焦煤：523家样本矿山：开工率（周）', '焦炭：230家独立焦化厂：产能利用率：中国（周）', '焦炭：230家独立焦化厂：日均产量：中国（周）', '中国:开工率(常减压开工率):山东地炼厂', '中国:产能利用率:成品油:独立炼厂', '中国:产能利用率:成品油:主营炼厂', '乙烯：MTO：生产企业：产能利用率：中国（周）', '乙烯：轻烃裂解：生产企业：产能利用率：中国（周）', '乙烯：石脑油裂解：生产企业：产能利用率：中国（周）', '乙烯：MTO：产量：中国（周）', '乙烯：产量：中国（周）', '乙烯：轻烃裂解：产量：中国（周）', '乙烯：石脑油裂解：产量：中国（周）', '乙烯：市场价：华东地区（周）', '氯化铵：产量：中国（周）', '氯化铵：产能利用率：中国（周）', '三聚氰胺：产量：中国（周）', '三聚氰胺：产能利用率：中国（周）', '尿素：产量：中国（周）', '尿素：产能利用率：中国（周）', '磷酸一铵：工业级：产能利用率：中国（周）', '磷酸一铵：工业级：产量：中国（周）', '磷酸一铵：产量：中国（周）', '磷酸二铵：产量：中国（周）', '硫酸钾：开工率：中国（周）', '氯化钾：产能利用率：中国（周）', '中国:开工率:精对苯二甲酸', '中国:江浙地区:开工率:涤纶长丝', '中国:装置负荷率:涤纶短纤', 'PTA：产能利用率：中国（周）', 'PTA：产量：中国（周）', 'MEG：产能利用率：中国（周）', 'MEG：产量：中国（周）', '聚酯：产能利用率：中国（周）', '聚酯：产量：中国（周）', 'PE：产能利用率：中国（周）', 'PE：化工生产企业：产量：中国（周）', 'PE：社会库存：中国（周）', 'PP：注塑：开工率：中国（周）', 'PP：产能利用率：中国（周）', 'PP：产量：中国（周）', 'PVC：产能利用率：中国（周）', 'PVC：产量：中国（周）', '中国:开工率:汽车轮胎(半钢胎)', '中国:开工率:汽车轮胎(全钢胎)', '制造业PMI', '制造业PMI:生产', '制造业PMI:新订单', '制造业PMI:新出口订单', '制造业PMI:从业人员', '中国：可再生能源：发电量（月）', '中国：火力发电：发电量（月）']
2025-06-16 10:09:21.644854: 
4️⃣ 测试模型训练...
2025-06-16 10:09:21.645298: 
🤖 测试模型训练模块...
2025-06-16 10:09:22.860155: ✅ 训练模块导入成功
2025-06-16 10:09:22.861013: 🎯 变量选择:
2025-06-16 10:09:22.862813:    目标变量: 工业增加值同比增速_月度_同花顺
2025-06-16 10:09:22.863731:    可用预测变量数量: 76
2025-06-16 10:09:22.864006:    使用全部预测变量数量: 76
2025-06-16 10:09:22.864301:    前10个预测变量: ['规模以上工业增加值:当月同比', '中国:日均产量:生铁:重点企业', '中国:日均产量:粗钢:重点企业', '重点电厂:日耗量:煤炭', '中国:主要45港口:日均疏港量:铁矿石', '中国:生产率:焦炉:国内独立焦化厂(230家)', '中国:高炉开工率(247家)', '中国:产量:螺纹钢:主要钢厂', '中国:开工率:线材:主要钢厂', '中国:产量:线材:主要钢厂']
2025-06-16 10:09:22.864557:    后10个预测变量: ['PVC：产量：中国（周）', '中国:开工率:汽车轮胎(半钢胎)', '中国:开工率:汽车轮胎(全钢胎)', '制造业PMI', '制造业PMI:生产', '制造业PMI:新订单', '制造业PMI:新出口订单', '制造业PMI:从业人员', '中国：可再生能源：发电量（月）', '中国：火力发电：发电量（月）']
2025-06-16 10:09:22.865076: 🚀 开始模型训练，参数:
2025-06-16 10:09:22.865572:    target_variable: 工业增加值同比增速_月度_同花顺
2025-06-16 10:09:22.866342:    selected_indicators: ['规模以上工业增加值:当月同比', '中国:日均产量:生铁:重点企业', '中国:日均产量:粗钢:重点企业', '重点电厂:日耗量:煤炭', '中国:主要45港口:日均疏港量:铁矿石', '中国:生产率:焦炉:国内独立焦化厂(230家)', '中国:高炉开工率(247家)', '中国:产量:螺纹钢:主要钢厂', '中国:开工率:线材:主要钢厂', '中国:产量:线材:主要钢厂', '中国:产量:冷轧板卷:主要钢厂', '中国:产量:热轧板卷:主要钢厂', '中国:产量:中厚板:主要钢厂', '中国:产量:钢材:重点钢铁企业', '中国:开工率:产能(>200万吨):焦化企业(230家)', '中国:开工率:产能(100-200万吨):焦化企业(230家)', '中国:主流港口:库存量:煤炭', '中国:库存量:焦炭:国内样本钢厂(247家)', '动力煤：462家样本矿山：产能利用率（周）', '动力煤：462家样本矿山：日均产量（周）', '精煤：样本洗煤厂（110家）：日均产量：中国（周）', '煤炭：样本洗煤厂（110家）：开工率：中国（周）', '精煤：523家样本矿山：日均产量（周）', '原煤：523家样本矿山：日均产量（周）', '炼焦煤：523家样本矿山：开工率（周）', '焦炭：230家独立焦化厂：产能利用率：中国（周）', '焦炭：230家独立焦化厂：日均产量：中国（周）', '中国:开工率(常减压开工率):山东地炼厂', '中国:产能利用率:成品油:独立炼厂', '中国:产能利用率:成品油:主营炼厂', '乙烯：MTO：生产企业：产能利用率：中国（周）', '乙烯：轻烃裂解：生产企业：产能利用率：中国（周）', '乙烯：石脑油裂解：生产企业：产能利用率：中国（周）', '乙烯：MTO：产量：中国（周）', '乙烯：产量：中国（周）', '乙烯：轻烃裂解：产量：中国（周）', '乙烯：石脑油裂解：产量：中国（周）', '乙烯：市场价：华东地区（周）', '氯化铵：产量：中国（周）', '氯化铵：产能利用率：中国（周）', '三聚氰胺：产量：中国（周）', '三聚氰胺：产能利用率：中国（周）', '尿素：产量：中国（周）', '尿素：产能利用率：中国（周）', '磷酸一铵：工业级：产能利用率：中国（周）', '磷酸一铵：工业级：产量：中国（周）', '磷酸一铵：产量：中国（周）', '磷酸二铵：产量：中国（周）', '硫酸钾：开工率：中国（周）', '氯化钾：产能利用率：中国（周）', '中国:开工率:精对苯二甲酸', '中国:江浙地区:开工率:涤纶长丝', '中国:装置负荷率:涤纶短纤', 'PTA：产能利用率：中国（周）', 'PTA：产量：中国（周）', 'MEG：产能利用率：中国（周）', 'MEG：产量：中国（周）', '聚酯：产能利用率：中国（周）', '聚酯：产量：中国（周）', 'PE：产能利用率：中国（周）', 'PE：化工生产企业：产量：中国（周）', 'PE：社会库存：中国（周）', 'PP：注塑：开工率：中国（周）', 'PP：产能利用率：中国（周）', 'PP：产量：中国（周）', 'PVC：产能利用率：中国（周）', 'PVC：产量：中国（周）', '中国:开工率:汽车轮胎(半钢胎)', '中国:开工率:汽车轮胎(全钢胎)', '制造业PMI', '制造业PMI:生产', '制造业PMI:新订单', '制造业PMI:新出口订单', '制造业PMI:从业人员', '中国：可再生能源：发电量（月）', '中国：火力发电：发电量（月）']
2025-06-16 10:09:22.866745:    training_start_date: 2020-01-03
2025-06-16 10:09:22.867176:    validation_start_date: 2024-01-05
2025-06-16 10:09:22.867536:    validation_end_date: 2024-12-31
2025-06-16 10:09:22.867814:    n_factors: 3
2025-06-16 10:09:22.868184:    em_max_iter: 50
2025-06-16 10:09:22.868585:    output_base_dir: test_outputs
2025-06-16 10:09:22.869307:    var_industry_map: {'pp:产能利用率:中国(周)': '橡胶塑料', '磷酸一铵:工业级:产量:中国(周)': '化学化工', '中国:可再生能源:发电量(月)': '电力', '中国:火力发电:发电量(月)': '电力', '原煤:523家样本矿山:日均产量(周)': '煤炭', '中国:开工率:精对苯二甲酸': '化纤', '尿素:产能利用率:中国(周)': '化学化工', '聚酯:产量:中国(周)': '化纤', '氯化铵:产能利用率:中国(周)': '化学化工', '制造业pmi:新订单': 'PMI', 'pe:产能利用率:中国(周)': '橡胶塑料', 'pe:社会库存:中国(周)': '橡胶塑料', 'pta:产量:中国(周)': '化纤', '中国:产量:中厚板:主要钢厂': '钢铁', '动力煤:462家样本矿山:产能利用率(周)': '煤炭', 'pp:产量:中国(周)': '橡胶塑料', '精煤:523家样本矿山:日均产量(周)': '煤炭', '重点电厂:日耗量:煤炭': '电力', '乙烯:石脑油裂解:产量:中国(周)': '化学化工', 'pta:产能利用率:中国(周)': '化纤', '乙烯:产量:中国(周)': '化学化工', '中国:生产率:焦炉:国内独立焦化厂(230家)': '钢铁', '中国:产能利用率:成品油:主营炼厂': '油气', '中国:开工率:产能(>200万吨):焦化企业(230家)': '煤炭', '精煤:样本洗煤厂(110家):日均产量:中国(周)': '煤炭', '三聚氰胺:产量:中国(周)': '化学化工', '中国:库存量:焦炭:国内样本钢厂(247家)': '煤炭', '中国:产量:热轧板卷:主要钢厂': '钢铁', '磷酸二铵:产量:中国(周)': '化学化工', '中国:主要45港口:日均疏港量:铁矿石': '钢铁', '中国:产量:冷轧板卷:主要钢厂': '钢铁', '中国:日均产量:生铁:重点企业': '钢铁', '乙烯:轻烃裂解:生产企业:产能利用率:中国(周)': '化学化工', '焦炭:230家独立焦化厂:日均产量:中国(周)': '煤炭', '中国:主流港口:库存量:煤炭': '煤炭', '中国:开工率(常减压开工率):山东地炼厂': '油气', '磷酸一铵:工业级:产能利用率:中国(周)': '化学化工', '中国:装置负荷率:涤纶短纤': '化纤', '中国:开工率:线材:主要钢厂': '钢铁', '聚酯:产能利用率:中国(周)': '化纤', 'pp:注塑:开工率:中国(周)': '橡胶塑料', '中国:开工率:汽车轮胎(半钢胎)': '汽车', '炼焦煤:523家样本矿山:开工率(周)': '煤炭', '磷酸一铵:产量:中国(周)': '化学化工', '中国:开工率:汽车轮胎(全钢胎)': '汽车', '中国:高炉开工率(247家)': '钢铁', 'pvc:产量:中国(周)': '橡胶塑料', '中国:产量:螺纹钢:主要钢厂': '钢铁', '乙烯:石脑油裂解:生产企业:产能利用率:中国(周)': '化学化工', '氯化钾:产能利用率:中国(周)': '化学化工', '中国:产量:线材:主要钢厂': '钢铁', '三聚氰胺:产能利用率:中国(周)': '化学化工', 'meg:产量:中国(周)': '化纤', '尿素:产量:中国(周)': '化学化工', '硫酸钾:开工率:中国(周)': '化学化工', '乙烯:市场价:华东地区(周)': '化学化工', '中国:江浙地区:开工率:涤纶长丝': '化纤', '制造业pmi:从业人员': 'PMI', '氯化铵:产量:中国(周)': '化学化工', '焦炭:230家独立焦化厂:产能利用率:中国(周)': '煤炭', 'pe:化工生产企业:产量:中国(周)': '橡胶塑料', '制造业pmi': 'PMI', 'meg:产能利用率:中国(周)': '化纤', '中国:产量:钢材:重点钢铁企业': '钢铁', '煤炭:样本洗煤厂(110家):开工率:中国(周)': '煤炭', '中国:开工率:产能(100-200万吨):焦化企业(230家)': '煤炭', 'pvc:产能利用率:中国(周)': '橡胶塑料', '中国:产能利用率:成品油:独立炼厂': '油气', '乙烯:mto:产量:中国(周)': '化学化工', '乙烯:轻烃裂解:产量:中国(周)': '化学化工', '动力煤:462家样本矿山:日均产量(周)': '煤炭', '制造业pmi:新出口订单': 'PMI', '乙烯:mto:生产企业:产能利用率:中国(周)': '化学化工', '制造业pmi:生产': 'PMI', '规模以上工业增加值:当月同比': '工业增加值同比增速', '中国:日均产量:粗钢:重点企业': '钢铁'}
2025-06-16 10:09:22.869833:    enable_hyperparameter_tuning: False
2025-06-16 10:09:22.870647:    enable_variable_selection: True
2025-06-16 10:09:22.871400:    enable_detailed_analysis: True
2025-06-16 10:09:22.871877:    generate_excel_report: True
2025-06-16 10:09:22.944174: ✅ 模型训练成功!
2025-06-16 10:09:22.944672: 生成的文件:
2025-06-16 10:09:22.945358:   ✅ final_model_joblib: test_outputs\final_dfm_model.joblib (1073 bytes)
2025-06-16 10:09:22.945735:   ✅ metadata: test_outputs\final_dfm_metadata.pkl (1500 bytes)
2025-06-16 10:09:22.946859: 
============================================================
2025-06-16 10:09:22.947226: 📊 测试结果总结
2025-06-16 10:09:22.947975: ============================================================
2025-06-16 10:09:22.948942: environment_setup: ✅ 成功
2025-06-16 10:09:22.949458: data_file_check: ✅ 成功
2025-06-16 10:09:22.949852: data_preparation: ✅ 成功
2025-06-16 10:09:22.950114: model_training: ✅ 成功
2025-06-16 10:09:22.950525: 
🎉 所有测试都成功完成!
2025-06-16 10:09:22.950874: ✅ DFM模型训练流程正常工作
2025-06-16 10:09:22.951525: 
📄 详细结果已保存到 test_results.txt
2025-06-16 10:09:22.951920: 📄 详细执行日志已保存到 test_execution_log.txt
