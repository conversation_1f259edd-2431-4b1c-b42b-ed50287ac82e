# -*- coding: utf-8 -*-
"""
DFM分析工具模块
提供PCA方差解释、因子贡献分析、R²计算等功能
"""

import time
import numpy as np
import pandas as pd
from sklearn.decomposition import PCA
from sklearn.impute import SimpleImputer
from sklearn.metrics import mean_squared_error, mean_absolute_error
import statsmodels.api as sm
from typing import Optional, Dict, List, Tuple, Any
import logging

# 导入配置模块
try:
    from ..config import AlgorithmDefaults, PerformanceDefaults
    _CONFIG_AVAILABLE = True
except ImportError:
    # 向后兼容：如果导入失败则使用硬编码值
    _CONFIG_AVAILABLE = False

# 配置日志
logger = logging.getLogger(__name__)

def calculate_pca_variance(
    data_standardized: pd.DataFrame,
    n_components: int,
    impute_strategy: str = 'mean'
) -> Optional[pd.DataFrame]:
    """
    计算给定标准化数据的 PCA 解释方差。

    Args:
        data_standardized (pd.DataFrame): 输入的标准化数据 (行为时间，列为变量)。
        n_components (int): 要提取的主成分数量。
        impute_strategy (str): 处理缺失值的策略 ('mean', 'median', 'most_frequent', or None).

    Returns:
        Optional[pd.DataFrame]:
            - pca_results_df: 包含 PCA 结果的 DataFrame (主成分, 解释方差%, 累计解释方差%),
                              如果发生错误或无法计算则返回 None。
    """
    print("\n计算 PCA 解释方差...")
    pca_results_df = None

    try:
        if data_standardized is None or data_standardized.empty:
            print("  错误: 输入的标准化数据为空，无法计算 PCA。")
            return None
        if n_components <= 0:
            print(f"  错误: 无效的主成分数量 ({n_components})。")
            return None
        
        data_for_pca = data_standardized.copy()
        print(f"  使用数据 (Shape: {data_for_pca.shape}) 进行 PCA 分析。")

        # 处理缺失值
        nan_count = data_for_pca.isna().sum().sum()
        data_pca_imputed_array = None # 初始化
        if nan_count > 0:
            if impute_strategy:
                print(f"  处理 PCA 输入数据的缺失值 (共 {nan_count} 个，使用策略: {impute_strategy})...")
                imputer = SimpleImputer(strategy=impute_strategy)
                # 直接获取 NumPy 数组
                data_pca_imputed_array = imputer.fit_transform(data_for_pca)
                print(f"  填充后 NumPy 数组 Shape: {data_pca_imputed_array.shape}")
                # 检查填充后是否仍有 NaN (理论上 SimpleImputer 不会留下)
                if np.isnan(data_pca_imputed_array).sum() > 0:
                    print("  错误: SimpleImputer 填充后仍存在 NaN。PCA 无法进行。")
                    return None
            else:
                print(f"  警告: 数据包含 {nan_count} 个 NaN，但未指定填充策略。PCA 可能失败。")
                data_pca_imputed_array = data_for_pca.to_numpy() # 转换为 NumPy 继续尝试
        else:
            data_pca_imputed_array = data_for_pca.to_numpy() # 无缺失值，直接转 NumPy
            print("  数据无缺失值，无需填充。")

        # 检查最终数组是否有效
        if data_pca_imputed_array is None or data_pca_imputed_array.shape[1] == 0:
            print("  错误: 处理/填充后数据为空或没有列，无法执行 PCA。")
            return None
        if data_pca_imputed_array.shape[1] < n_components:
            print(f"  警告: 处理/填充后数据列数 ({data_pca_imputed_array.shape[1]}) 少于请求的主成分数 ({n_components})。将使用 {data_pca_imputed_array.shape[1]} 作为主成分数。")
            n_components = data_pca_imputed_array.shape[1]
            if n_components == 0:
                print("  错误: 调整后主成分数为 0。无法执行 PCA。")
                return None

        # 执行 PCA (在 NumPy 数组上)
        # 使用配置的随机种子
        random_state = AlgorithmDefaults.RANDOM_SEED if _CONFIG_AVAILABLE else 42
        pca = PCA(n_components=n_components, random_state=random_state)  # 确保PCA结果的一致性
        print(f"  对处理/填充后的数据 (Shape: {data_pca_imputed_array.shape}) 执行 PCA (n_components={n_components})...")
        pca.fit(data_pca_imputed_array)

        explained_variance_ratio_pct = pca.explained_variance_ratio_ * 100
        cumulative_explained_variance_pct = np.cumsum(explained_variance_ratio_pct)

        pca_results_df = pd.DataFrame({
            '主成分 (PC)': [f'PC{i+1}' for i in range(n_components)],
            '解释方差 (%)': explained_variance_ratio_pct,
            '累计解释方差 (%)': cumulative_explained_variance_pct,
            '特征值 (Eigenvalue)': pca.explained_variance_
        })

        print("  PCA 解释方差计算完成:")
        print(pca_results_df.to_string(index=False))

    except Exception as e_pca_main:
        print(f"  计算 PCA 解释方差时发生错误: {e_pca_main}")
        import traceback
        traceback.print_exc()
        pca_results_df = None
        
    logger.debug(f"calculate_pca_variance 返回 pca_results_df 类型: {type(pca_results_df)}")
    return pca_results_df

def calculate_factor_contributions(
    dfm_results: object, 
    data_processed: pd.DataFrame, 
    target_variable: str, 
    n_factors: int
) -> Tuple[Optional[pd.DataFrame], Optional[Dict[str, float]]]:
    """
    计算 DFM 各因子对目标变量方差的贡献度。
    修正：使用 OLS 将原始尺度的目标变量对标准化因子回归，以获得正确的载荷。

    Args:
        dfm_results (object): DFM 模型运行结果对象 (需要包含 x_sm 属性, 即标准化因子)。
        data_processed (pd.DataFrame): DFM 模型输入的处理后数据 (包含原始/平稳化尺度的目标变量)。
        target_variable (str): 目标变量名称。
        n_factors (int): 模型使用的因子数量。

    Returns:
        Tuple[Optional[pd.DataFrame], Optional[Dict[str, float]]]: 
            - contribution_df: 包含各因子贡献度详情的 DataFrame，出错则为 None。
            - factor_contributions: 因子名称到总方差贡献度(%)的字典，出错则为 None。
    """
    print("\n计算各因子对目标变量的贡献度 (修正 OLS 方法)...")
    contribution_df = None
    factor_contributions_dict = None
    
    try:
        # 1. 提取标准化因子和原始目标变量
        if not (dfm_results and hasattr(dfm_results, 'x_sm') and isinstance(dfm_results.x_sm, pd.DataFrame)):
            print("  错误: DFM 结果对象无效或缺少 'x_sm' (标准化因子) 属性。")
            return None, None
        factors_std = dfm_results.x_sm
        print(f"  [DEBUG] Shape of factors_std: {factors_std.shape}")
        print(f"  [DEBUG] Value of n_factors passed to function: {n_factors}")
        # --- 结束调试打印 ---
        if not (data_processed is not None and target_variable in data_processed.columns):
            print("  错误: 'data_processed' 无效或不包含目标变量。")
            return None, None
        target_orig = data_processed[target_variable]

        # 确保因子数量有效 (修正类型检查 - 直接在 if 中修正)
        if not (isinstance(n_factors, (int, np.integer)) and n_factors > 0 and n_factors <= factors_std.shape[1]):
             print(f"  错误: 无效的因子数量 ({n_factors}, 类型: {type(n_factors)}) 或与因子矩阵维度不符 (Shape: {factors_std.shape})。")
             return None, None
        factors_std = factors_std.iloc[:, :n_factors] # 只选择实际使用的因子列

        print(f"  提取到标准化因子 (Shape: {factors_std.shape}) 和目标变量 (Length: {len(target_orig)})。")

        # 2. 对齐数据并处理缺失值以进行 OLS
        # 合并因子和目标变量，按索引对齐
        merged_data = pd.concat([target_orig, factors_std], axis=1).dropna()
        if merged_data.empty:
            print("  错误: 对齐因子和目标变量并移除 NaN 后数据为空，无法进行 OLS。")
            return None, None
            
        target_ols = merged_data[target_variable]
        factors_ols = merged_data[factors_std.columns]
        print(f"  OLS 使用的数据点数: {len(merged_data)}")

        # 添加常数项进行 OLS (因为 target_orig 未中心化)
        factors_ols_with_const = sm.add_constant(factors_ols)

        # 3. 执行 OLS: target_orig ~ const + factors_std
        print("  执行 OLS 回归: 目标变量 ~ 标准化因子...")
        ols_model = sm.OLS(target_ols, factors_ols_with_const)
        ols_results = ols_model.fit()
        
        # 提取因子对应的系数 (排除常数项)
        loadings_orig_scale = ols_results.params.drop('const', errors='ignore').values 
        if len(loadings_orig_scale) != n_factors:
             print(f"  错误: OLS 结果中的系数数量 ({len(loadings_orig_scale)}) 与预期因子数 ({n_factors}) 不匹配。")
             # 尝试从原始结果中按因子名提取？(更复杂，暂时先报错)
             return None, None 
        print(f"  成功从 OLS 提取 {len(loadings_orig_scale)} 个原始尺度载荷。")
        # print(f"  OLS R-squared: {ols_results.rsquared:.4f}") # 可选：打印 R 方

        # 4. 计算贡献度
        loading_sq_orig = loadings_orig_scale ** 2
        communality_orig_approx = np.sum(loading_sq_orig) # 近似共同度 (因子方差=1)
        target_variance_orig = np.nanvar(target_ols) # 使用 OLS 使用的数据计算方差
        
        # 使用配置的数值保护值
        zero_protection = AlgorithmDefaults.ZERO_PROTECTION if _CONFIG_AVAILABLE else 1e-9
        if target_variance_orig < zero_protection:
            print("  错误: 目标变量在 OLS 数据点上的方差过小，无法计算贡献度。")
            return None, None
            
        pct_contribution_total_orig = (loading_sq_orig / target_variance_orig) * 100

        # 对共同方差的贡献
        if communality_orig_approx > zero_protection:
            pct_contribution_common_orig = (loading_sq_orig / communality_orig_approx) * 100
        else:
            pct_contribution_common_orig = np.zeros_like(loading_sq_orig) * np.nan
            print("  警告: 近似共同度过小，无法计算对共同方差的百分比贡献。")

        # 创建结果 DataFrame
        contribution_df = pd.DataFrame({
            '因子 (Factor)': [f'Factor{i+1}' for i in range(n_factors)],
            '原始尺度载荷 (OLS Coef)': loadings_orig_scale,
            '平方载荷 (原始尺度)': loading_sq_orig,
            '对共同方差贡献 (%)[近似]': pct_contribution_common_orig,
            '对总方差贡献 (%)[近似]': pct_contribution_total_orig
        })
        contribution_df = contribution_df.sort_values(by='对总方差贡献 (%)[近似]', ascending=False)

        print("  各因子对目标变量方差贡献度计算完成 (基于 OLS 原始尺度载荷):")
        print(contribution_df.to_string(index=False, float_format="%.4f"))
        print(f"  目标变量总方差 (OLS样本): {target_variance_orig:.4f}")
        print(f"  近似共同度 (OLS样本, 因子方差=1): {communality_orig_approx:.4f}")
        print(f"  OLS R-squared (总解释方差比例): {ols_results.rsquared:.4f}")
            
        factor_contributions_dict = contribution_df.set_index('因子 (Factor)')['对总方差贡献 (%)[近似]'].to_dict()

    except Exception as e_contrib_main:
        print(f"  计算因子对目标变量贡献度时发生错误: {e_contrib_main}")
        import traceback
        traceback.print_exc()
        contribution_df = None # 确保出错时返回 None
        factor_contributions_dict = None
        
    return contribution_df, factor_contributions_dict 

def calculate_individual_variable_r2(
    dfm_results: object, 
    data_processed: pd.DataFrame, 
    variable_list: List[str], 
    n_factors: int,
    timeout_seconds: int = None  # 使用配置值
) -> Optional[Dict[str, pd.DataFrame]]:
    """
    计算每个因子与每个单独变量回归的 R 平方值。

    Args:
        dfm_results (object): DFM 模型运行结果对象 (需要包含 x_sm)。
        data_processed (pd.DataFrame): DFM 模型输入的处理后数据。
        variable_list (List[str]): 要计算 R 平方的变量列表。
        n_factors (int): 模型使用的因子数量。
        timeout_seconds (int): 超时时间（秒），使用配置默认值

    Returns:
        Optional[Dict[str, pd.DataFrame]]: 一个字典，键是因子名称 (Factor1, ...),
            值是包含 'Variable' 和 'R2' 列的排序 DataFrame。出错则返回 None。
    """
    # 使用配置的超时值
    if timeout_seconds is None:
        timeout_seconds = PerformanceDefaults.TIMEOUT_MEDIUM if _CONFIG_AVAILABLE else 120
        
    start_time = time.time()
    
    print("\n计算各因子对单个变量的解释力 (R-squared)...")
    r2_results_by_factor = {}
    
    try:
        # 1. 提取标准化因子
        if not (dfm_results and hasattr(dfm_results, 'x_sm') and isinstance(dfm_results.x_sm, pd.DataFrame)):
            print("  错误: DFM 结果对象无效或缺少 'x_sm' (标准化因子) 属性。")
            return None
        factors_std = dfm_results.x_sm
        print(f"  [DEBUG] Type of factors_std (dfm_results.x_sm): {type(factors_std)}")
        if isinstance(factors_std, pd.DataFrame):
            print(f"  [DEBUG] Shape of factors_std: {factors_std.shape}")
        print(f"  [DEBUG] Value of n_factors passed to function: {n_factors} (type: {type(n_factors)})")

        if not (isinstance(n_factors, (int, np.integer)) and n_factors > 0 and n_factors <= factors_std.shape[1]):
             print(f"  错误: 无效的因子数量 ({n_factors}, 类型: {type(n_factors)}) 或与因子矩阵维度不符 (Shape: {factors_std.shape})。")
             return None
        factors_std = factors_std.iloc[:, :n_factors] # 只选择实际使用的因子列
        factor_names = [f'Factor{i+1}' for i in range(n_factors)]
        
        # 验证数据和变量列表
        valid_variables = [var for var in variable_list if var in data_processed.columns]
        if not valid_variables:
            print("  错误: 没有有效的变量可用于计算 R²。")
            return None
        print(f"  找到 {len(valid_variables)} 个有效变量，将计算与 {n_factors} 个因子的回归 R²。")

        # 2. 遍历每个因子
        for factor_idx, factor_name in enumerate(factor_names):
            print(f"\n  计算 {factor_name} 对各变量的解释力...")
            factor_series = factors_std.iloc[:, factor_idx]
            factor_r2_list = []

            # 3. 遍历每个变量（加入进度监控）
            for var_idx, var in enumerate(valid_variables):
                elapsed_time = time.time() - start_time
                if elapsed_time > timeout_seconds:
                    print(f"    ⚠️ 计算超时 ({elapsed_time:.1f}s > {timeout_seconds}s)，停止计算")
                    break

                # 使用配置的进度报告间隔
                progress_interval = 10  # 可以添加到配置中
                if var_idx % progress_interval == 0 and var_idx > 0:
                    print(f"    进度: {var_idx}/{len(valid_variables)} 变量已处理 ({elapsed_time:.1f}s)")

                variable_series = data_processed[var]

                # 对齐数据并移除 NaN
                merged = pd.concat([variable_series, factor_series], axis=1).dropna()
                
                # 检查是否有足够的数据点
                if len(merged) < 2:
                    continue  # 跳过数据不足的变量
                
                Y = merged.iloc[:, 0] # Variable
                X = merged.iloc[:, 1] # Factor
                
                # 添加常数项
                X_with_const = sm.add_constant(X)
                
                try:
                    ols_model = sm.OLS(Y, X_with_const)
                    ols_results = ols_model.fit()
                    r_squared = ols_results.rsquared
                except:
                    r_squared = 0.0  # 回归失败时设为 0
                
                factor_r2_list.append({'Variable': var, 'R2': r_squared})

            # 检查是否因超时中断
            if elapsed_time > timeout_seconds:
                break

            # 4. 排序并存储结果
            if factor_r2_list:
                factor_df = pd.DataFrame(factor_r2_list)
                factor_df.sort_values(by='R2', ascending=False, inplace=True)
                r2_results_by_factor[factor_name] = factor_df
                print(f"    ✅ {factor_name} 计算完成，共 {len(factor_r2_list)} 个变量的 R²")
            else:
                print(f"    ⚠️ {factor_name} 无有效 R² 结果")

        total_time = time.time() - start_time
        if r2_results_by_factor:
            print(f"\n  R² 计算完成！成功计算了 {len(r2_results_by_factor)} 个因子的结果")
        else:
            print("\n  ⚠️ 未成功计算任何因子的 R² 结果")

    except Exception as e_main_r2:
        print(f"  计算因子对单个变量 R2 时发生主错误: {e_main_r2}")
        import traceback
        traceback.print_exc()
        r2_results_by_factor = {}

    total_time = time.time() - start_time
    print(f"  R-squared计算完成，总耗时: {total_time:.1f}秒")
    return r2_results_by_factor

def calculate_metrics_with_lagged_target(
    nowcast_series: pd.Series,
    target_series: pd.Series,
    validation_start: str,
    validation_end: str,
    train_end: str,
    target_variable_name: str = 'Target'
) -> Tuple[Dict[str, Optional[float]], Optional[pd.DataFrame]]:
    """
    计算 IS/OOS RMSE, MAE (基于周度比较) 和 Hit Rate (基于修改后的月度方向一致性)。

    RMSE/MAE 核心逻辑: 将 t 月份的实际目标值与该月内 *所有周* 的预测值进行比较。(保持不变)
    Hit Rate 核心逻辑 (新): 对每个月 m，比较其内部每周预测值 nowcast_w 相对于上月实际值 actual_{m-1} 的方向变化 sign(nowcast_w - actual_{m-1})，
                             是否与本月实际值 actual_m 相对于上月实际值 actual_{m-1} 的方向变化 sign(actual_m - actual_{m-1}) 一致。
                             月度 Hit Rate = 方向一致的周数 / 总周数。然后对月度 Hit Rate 求 IS/OOS 平均。

    Args:
        nowcast_series: 周度 Nowcast 序列 (DatetimeIndex)。
        target_series: 原始月度 Target 序列 (DatetimeIndex 代表实际数据发生的月份)。
                       **重要假设**: target_series 的值是该索引月份的 *实际* 值。
        validation_start: OOS 周期开始日期字符串。
        validation_end: OOS 周期结束日期字符串。
        train_end: IS 周期结束日期字符串。
        target_variable_name: 输出 DataFrame 中目标变量列的名称。

    Returns:
        Tuple 包含:
            - 包含指标的字典: is_rmse, oos_rmse, is_mae, oos_mae (周度计算),
              is_hit_rate, oos_hit_rate (基于新的月度方向一致性计算)。 (计算失败则为 NaN)。
            - 用于 *RMSE/MAE* 计算的周度对齐 DataFrame (aligned_df_weekly)，对齐失败则为 None。
              (注意: 此 DataFrame 不直接用于新的 Hit Rate 计算)。
    """
    
    # 🔥 DEBUG: 添加详细的debug打印
    print(f"\n=== [NEW CODE] calculate_metrics_with_lagged_target DEBUG START ===")
    print(f"Input parameters:")
    print(f"  nowcast_series shape: {nowcast_series.shape}")
    print(f"  target_series shape: {target_series.shape}")
    print(f"  validation_start: {validation_start}")
    print(f"  validation_end: {validation_end}")
    print(f"  train_end: {train_end}")
    print(f"  target_variable_name: {target_variable_name}")
    
    if not nowcast_series.empty:
        nowcast_describe = nowcast_series.describe()
        print(f"  nowcast_series stats: count={nowcast_describe['count']}, mean={nowcast_describe['mean']:.4f}, std={nowcast_describe['std']:.4f}")
        print(f"  nowcast_series index range: {nowcast_series.index.min()} to {nowcast_series.index.max()}")
    else:
        print(f"  nowcast_series is EMPTY!")
        
    if not target_series.empty:
        target_describe = target_series.describe()
        print(f"  target_series stats: count={target_describe['count']}, mean={target_describe['mean']:.4f}, std={target_describe['std']:.4f}")
        print(f"  target_series index range: {target_series.index.min()} to {target_series.index.max()}")
    else:
        print(f"  target_series is EMPTY!")
    
    metrics = {
        'is_rmse': np.nan, 'oos_rmse': np.nan,
        'is_mae': np.nan, 'oos_mae': np.nan,
        'is_hit_rate': np.nan, 'oos_hit_rate': np.nan
    }
    aligned_df_weekly = None # 用于 RMSE/MAE 计算
    # aligned_df_monthly_for_hit_rate = None # 不再需要这个变量名

    try:
        # --- 输入验证 (基本保持不变) ---
        if nowcast_series is None or nowcast_series.empty:
            logger.error("Error (calc_metrics_new_hr): Nowcast series is empty.")
            return metrics, None
        if target_series is None or target_series.empty:
            logger.error("Error (calc_metrics_new_hr): Target series is empty.")
            return metrics, None
        # 确保索引是 DatetimeIndex
        for series, name in [(nowcast_series, 'nowcast'), (target_series, 'target')]:
            if not isinstance(series.index, pd.DatetimeIndex):
                try:
                    series.index = pd.to_datetime(series.index)
                except Exception as e:
                    logger.error(f"Error (calc_metrics_new_hr): Failed to convert {name} index to DatetimeIndex: {e}")
                    return metrics, None
        # --- 结束输入验证 ---

        # --- 准备时间点 ---
        try:
            oos_start_dt = pd.to_datetime(validation_start)
            oos_end_dt = pd.to_datetime(validation_end) if validation_end else nowcast_series.index.max() # Use nowcast end if None
            is_end_dt = pd.to_datetime(train_end)
            
            print(f"Date conversion:")
            print(f"  oos_start_dt: {oos_start_dt}")
            print(f"  oos_end_dt: {oos_end_dt}")
            print(f"  is_end_dt: {is_end_dt}")
            
        except Exception as e:
            logger.error(f"Error parsing date strings (train_end, validation_start, validation_end): {e}")
            return metrics, None

        # --- 计算 RMSE/MAE (基于周度比较 - 逻辑保持不变) ---
        # --- 准备月度目标数据 (仅用于 RMSE/MAE 的合并) ---
        df_target_monthly_for_rmse = target_series.to_frame(name=target_variable_name).copy()
        df_target_monthly_for_rmse['YearMonth'] = df_target_monthly_for_rmse.index.to_period('M')
        # 处理重复月 (保留最后一个) - 以防万一
        if df_target_monthly_for_rmse['YearMonth'].duplicated().any():
            df_target_monthly_for_rmse = df_target_monthly_for_rmse.groupby('YearMonth').last()
        else:
            df_target_monthly_for_rmse = df_target_monthly_for_rmse.set_index('YearMonth')

        print(f"Monthly target data for RMSE/MAE calculation:")
        print(f"  df_target_monthly_for_rmse shape: {df_target_monthly_for_rmse.shape}")
        print(f"  df_target_monthly_for_rmse head:")
        print(df_target_monthly_for_rmse.head())

        try:
            # 1. 准备周度预测
            df_nowcast_weekly = nowcast_series.to_frame(name='Nowcast').copy()
            df_nowcast_weekly['YearMonth'] = df_nowcast_weekly.index.to_period('M')

            print(f"Weekly nowcast data:")
            print(f"  df_nowcast_weekly shape: {df_nowcast_weekly.shape}")
            print(f"  df_nowcast_weekly head:")
            print(df_nowcast_weekly.head())

            # 2. 合并周度预测和月度目标 (用于 RMSE/MAE)
            aligned_df_weekly = pd.merge(
                df_nowcast_weekly,
                df_target_monthly_for_rmse[[target_variable_name]], # 使用准备好的月度目标
                left_on='YearMonth',
                right_index=True,
                how='left'
            ).drop(columns=['YearMonth']) # YearMonth 列不再需要

            print(f"Aligned weekly data:")
            print(f"  aligned_df_weekly shape: {aligned_df_weekly.shape}")
            print(f"  aligned_df_weekly head:")
            print(aligned_df_weekly.head())
            print(f"  aligned_df_weekly NaN counts: {aligned_df_weekly.isnull().sum()}")

            if aligned_df_weekly.empty or aligned_df_weekly[target_variable_name].isnull().all():
                logger.warning("Warning (calc_metrics_new_hr): Weekly alignment for RMSE/MAE resulted in empty data or all NaNs for target.")
                # 继续尝试计算 Hit Rate
            else:
                # 3. 分割 IS/OOS (周度)
                # 使用之前转换好的 datetime 对象
                aligned_is_weekly = aligned_df_weekly[aligned_df_weekly.index <= is_end_dt].dropna()
                aligned_oos_weekly = aligned_df_weekly[(aligned_df_weekly.index >= oos_start_dt) & (aligned_df_weekly.index <= oos_end_dt)].dropna()

                print(f"IS/OOS data split:")
                print(f"  aligned_is_weekly shape: {aligned_is_weekly.shape}")
                print(f"  aligned_oos_weekly shape: {aligned_oos_weekly.shape}")

                # 4. 计算周度 RMSE/MAE
                if not aligned_is_weekly.empty:
                    metrics['is_rmse'] = np.sqrt(mean_squared_error(aligned_is_weekly[target_variable_name], aligned_is_weekly['Nowcast']))
                    metrics['is_mae'] = mean_absolute_error(aligned_is_weekly[target_variable_name], aligned_is_weekly['Nowcast'])
                    print(f"IS RMSE/MAE calculated: RMSE={metrics['is_rmse']:.4f}, MAE={metrics['is_mae']:.4f}")
                    # logger.debug(f"IS RMSE/MAE (weekly, {len(aligned_is_weekly)} pts): RMSE={metrics['is_rmse']:.4f}, MAE={metrics['is_mae']:.4f}")
                if not aligned_oos_weekly.empty:
                    metrics['oos_rmse'] = np.sqrt(mean_squared_error(aligned_oos_weekly[target_variable_name], aligned_oos_weekly['Nowcast']))
                    metrics['oos_mae'] = mean_absolute_error(aligned_oos_weekly[target_variable_name], aligned_oos_weekly['Nowcast'])
                    print(f"OOS RMSE/MAE calculated: RMSE={metrics['oos_rmse']:.4f}, MAE={metrics['oos_mae']:.4f}")
                    # logger.debug(f"OOS RMSE/MAE (weekly, {len(aligned_oos_weekly)} pts): RMSE={metrics['oos_rmse']:.4f}, MAE={metrics['oos_mae']:.4f}")
                else:
                    logger.warning("Warning (calc_metrics_new_hr): OOS period has no valid weekly aligned data points after dropna() for RMSE/MAE.")
        except Exception as e_rmse_mae:
             logger.error(f"Error calculating weekly RMSE/MAE: {type(e_rmse_mae).__name__}: {e_rmse_mae}", exc_info=True)
             # 即使 RMSE/MAE 计算失败，也继续尝试计算 Hit Rate


        # --- 计算 Hit Rate (基于新的月度方向一致性逻辑) ---
        print(f"\nCalculating Hit Rate...")
        try:
            # 1. 准备数据
            nowcast_df = nowcast_series.to_frame('Nowcast').copy()
            nowcast_df['NowcastMonth'] = nowcast_df.index.to_period('M') # 月份周期

            target_df = target_series.to_frame(target_variable_name).copy()
            target_df['TargetMonth'] = target_df.index.to_period('M')
            target_df = target_df.groupby('TargetMonth').last() # 确保每月只有一个目标值
            target_df_lagged = target_df.shift(1) # 获取上个月目标值
            target_df_lagged.columns = [f'{target_variable_name}_Lagged'] # 重命名

            print(f"Hit Rate data preparation:")
            print(f"  nowcast_df shape: {nowcast_df.shape}")
            print(f"  target_df shape after groupby: {target_df.shape}")
            print(f"  target_df_lagged shape: {target_df_lagged.shape}")

            # 2. 合并数据
            # 将本月目标和上月目标合并到周度预测数据中
            merged_hr = pd.merge(
                nowcast_df,
                target_df[[target_variable_name]],
                left_on='NowcastMonth',
                right_index=True,
                how='left' # 保留所有周预测
            )
            merged_hr = pd.merge(
                merged_hr,
                target_df_lagged[[f'{target_variable_name}_Lagged']],
                left_on='NowcastMonth',
                right_index=True,
                how='left'
            )

            print(f"Merged Hit Rate data:")
            print(f"  merged_hr shape before dropna: {merged_hr.shape}")
            print(f"  merged_hr columns: {merged_hr.columns.tolist()}")
            print(f"  merged_hr head:")
            print(merged_hr.head())

            # 3. 移除无法计算方向的行
            merged_hr.dropna(subset=['Nowcast', target_variable_name, f'{target_variable_name}_Lagged'], inplace=True)

            print(f"  merged_hr shape after dropna: {merged_hr.shape}")

            if merged_hr.empty:
                logger.warning("Warning (calc_metrics_new_hr): No valid data points after merging and dropping NaNs for Hit Rate calculation.")
            else:
                # 4. 计算方向
                actual_diff = merged_hr[target_variable_name] - merged_hr[f'{target_variable_name}_Lagged']
                predicted_diff = merged_hr['Nowcast'] - merged_hr[f'{target_variable_name}_Lagged']

                print(f"Direction calculation:")
                print(f"  actual_diff describe: {actual_diff.describe()}")
                print(f"  predicted_diff describe: {predicted_diff.describe()}")

                # 使用 np.sign 处理 0 值情况 (sign(0)=0)
                actual_direction = np.sign(actual_diff)
                predicted_direction = np.sign(predicted_diff)

                print(f"  actual_direction unique values: {actual_direction.unique()}")
                print(f"  predicted_direction unique values: {predicted_direction.unique()}")

                # 5. 判断方向是否一致 (注意: 0 == 0 会被算作命中)
                merged_hr['Hit'] = (actual_direction == predicted_direction).astype(int)

                print(f"  Hit calculation: {merged_hr['Hit'].sum()} hits out of {len(merged_hr)} total ({merged_hr['Hit'].mean()*100:.2f}%)")

                # 6. 计算月度命中率
                monthly_hit_rate = merged_hr.groupby('NowcastMonth')['Hit'].mean() * 100

                print(f"Monthly hit rates:")
                print(f"  monthly_hit_rate shape: {monthly_hit_rate.shape}")
                print(f"  monthly_hit_rate describe: {monthly_hit_rate.describe()}")

                # 7. 分割 IS/OOS (月度)
                # 转换 PeriodIndex 为 DatetimeIndex (月底) 以便比较
                monthly_hit_rate.index = monthly_hit_rate.index.to_timestamp(how='end')

                # 使用之前转换好的 datetime 对象进行分割
                is_monthly_hr = monthly_hit_rate[monthly_hit_rate.index <= is_end_dt]
                oos_monthly_hr = monthly_hit_rate[(monthly_hit_rate.index >= oos_start_dt) & (monthly_hit_rate.index <= oos_end_dt)]

                print(f"IS/OOS hit rate split:")
                print(f"  is_monthly_hr shape: {is_monthly_hr.shape}")
                print(f"  oos_monthly_hr shape: {oos_monthly_hr.shape}")

                # 8. 计算平均月度命中率
                if not is_monthly_hr.empty:
                    metrics['is_hit_rate'] = is_monthly_hr.mean()
                    print(f"IS Hit Rate calculated: {metrics['is_hit_rate']:.2f}%")
                    logger.debug(f"IS HitRate (new, avg monthly, {len(is_monthly_hr)} months): {metrics['is_hit_rate']:.2f}%")
                if not oos_monthly_hr.empty:
                    metrics['oos_hit_rate'] = oos_monthly_hr.mean()
                    print(f"OOS Hit Rate calculated: {metrics['oos_hit_rate']:.2f}%")
                    logger.debug(f"OOS HitRate (new, avg monthly, {len(oos_monthly_hr)} months): {metrics['oos_hit_rate']:.2f}%")
                else:
                    logger.warning("Warning (calc_metrics_new_hr): OOS period has no valid monthly hit rates.")

        except Exception as e_hit_rate:
             logger.error(f"Error calculating new Hit Rate: {type(e_hit_rate).__name__}: {e_hit_rate}", exc_info=True)
             # Hit Rate 将保持 NaN

        # 格式化最终输出字典 (可选)
        metrics_formatted = {k: f"{v:.4f}" if pd.notna(v) else "N/A" for k, v in metrics.items()}
        print(f"\nFinal metrics calculated:")
        print(f"  IS RMSE: {metrics['is_rmse']}")
        print(f"  OOS RMSE: {metrics['oos_rmse']}")
        print(f"  IS MAE: {metrics['is_mae']}")
        print(f"  OOS MAE: {metrics['oos_mae']}")
        print(f"  IS Hit Rate: {metrics['is_hit_rate']}")
        print(f"  OOS Hit Rate: {metrics['oos_hit_rate']}")
        print(f"=== [NEW CODE] calculate_metrics_with_lagged_target DEBUG END ===\n")
        # logger.info(f"Calculated Metrics (RMSE/MAE weekly, HitRate new monthly): {metrics_formatted}") # <<< 注释掉

    except Exception as e:
        logger.error(f"Error during metrics calculation: {type(e).__name__}: {e}", exc_info=True)
        print(f"=== [NEW CODE] calculate_metrics_with_lagged_target DEBUG END (ERROR) ===\n")
        # 返回初始 NaN 字典和 None DataFrame
        return {k: np.nan for k in metrics}, None

    # 返回包含 NaN 或计算值的指标字典，以及用于 RMSE/MAE 计算的周度对齐 DataFrame
    return metrics, aligned_df_weekly

# <<< 新增函数：计算行业 R2 >>>
def calculate_industry_r2(
    dfm_results: Any,
    data_processed: pd.DataFrame,
    variable_list: List[str],
    var_industry_map: Dict[str, str],
    n_factors: int,
    timeout_seconds: int = None  # 使用配置值
) -> Optional[pd.Series]:
    """
    计算行业层面的因子R²。

    Args:
        dfm_results: DFM模型结果
        data_processed: 处理后的数据
        variable_list: 变量列表
        var_industry_map: 变量到行业的映射
        n_factors: 因子数量
        timeout_seconds: 超时时间（秒），使用配置默认值

    Returns:
        行业R²的Series，失败时返回None
    """
    # 使用配置的超时值
    if timeout_seconds is None:
        timeout_seconds = PerformanceDefaults.TIMEOUT_MEDIUM if _CONFIG_AVAILABLE else 120
        
    print("\n计算行业层面的因子 R²...")
    start_time = time.time()
    
    try:
        # 验证输入
        if not (dfm_results and hasattr(dfm_results, 'x_sm')):
            print("  错误: DFM结果无效")
            return None
        
        factors_std = dfm_results.x_sm.iloc[:, :n_factors]
        
        # 按行业分组变量
        industry_groups = {}
        for var in variable_list:
            if var in var_industry_map:
                industry = var_industry_map[var]
                if industry not in industry_groups:
                    industry_groups[industry] = []
                industry_groups[industry].append(var)
        
        if not industry_groups:
            print("  错误: 没有找到有效的行业分组")
            return None
        
        industry_r2_results = {}
        
        for industry_idx, (industry, vars_in_industry) in enumerate(industry_groups.items()):
            elapsed_time = time.time() - start_time
            if elapsed_time > timeout_seconds:
                print(f"    ⚠️ 行业R²计算超时 ({elapsed_time:.1f}s > {timeout_seconds}s)")
                break
                
            print(f"  处理行业: {industry} ({len(vars_in_industry)} 个变量)")
            
            # 获取该行业的变量数据
            industry_data = data_processed[vars_in_industry].dropna(how='all')
            if industry_data.empty:
                continue
            
            # 计算行业均值作为代表
            industry_mean = industry_data.mean(axis=1).dropna()
            
            # 与因子对齐
            aligned_data = pd.concat([industry_mean, factors_std], axis=1).dropna()
            if len(aligned_data) < 2:
                continue
            
            Y = aligned_data.iloc[:, 0]  # 行业均值
            X = aligned_data.iloc[:, 1:]  # 所有因子
            
            # 添加常数项并回归
            X_with_const = sm.add_constant(X)
            try:
                ols_model = sm.OLS(Y, X_with_const)
                ols_results = ols_model.fit()
                industry_r2_results[industry] = ols_results.rsquared
            except:
                industry_r2_results[industry] = 0.0
        
        if industry_r2_results:
            result_series = pd.Series(industry_r2_results).sort_values(ascending=False)
            print(f"  ✅ 行业R²计算完成，共 {len(result_series)} 个行业")
            return result_series
        else:
            print("  ⚠️ 没有成功计算任何行业的R²")
            return None
            
    except Exception as e:
        print(f"  计算行业R²时发生错误: {e}")
        return None

# <<< 新增函数：计算每个因子对每个行业的 R2 >>>
def calculate_factor_industry_r2(
    dfm_results: Any,
    data_processed: pd.DataFrame,
    variable_list: List[str],
    var_industry_map: Dict[str, str],
    n_factors: int,
    timeout_seconds: int = None  # 使用配置值
) -> Optional[Dict[str, pd.Series]]:
    """
    计算每个因子对各行业的R²。

    Args:
        dfm_results: DFM模型结果
        data_processed: 处理后的数据
        variable_list: 变量列表
        var_industry_map: 变量到行业的映射
        n_factors: 因子数量
        timeout_seconds: 超时时间（秒），使用配置默认值

    Returns:
        字典，键为因子名，值为行业R²的Series，失败时返回None
    """
    # 使用配置的超时值
    if timeout_seconds is None:
        timeout_seconds = PerformanceDefaults.TIMEOUT_MEDIUM if _CONFIG_AVAILABLE else 120
        
    print("\n计算各因子对行业的解释力...")
    start_time = time.time()
    
    try:
        # 验证输入
        if not (dfm_results and hasattr(dfm_results, 'x_sm')):
            print("  错误: DFM结果无效")
            return None
        
        factors_std = dfm_results.x_sm.iloc[:, :n_factors]
        factor_names = [f'Factor{i+1}' for i in range(n_factors)]
        
        # 按行业分组变量
        industry_groups = {}
        for var in variable_list:
            if var in var_industry_map:
                industry = var_industry_map[var]
                if industry not in industry_groups:
                    industry_groups[industry] = []
                industry_groups[industry].append(var)
        
        if not industry_groups:
            print("  错误: 没有找到有效的行业分组")
            return None
        
        factor_industry_r2 = {}
        
        for factor_idx, factor_name in enumerate(factor_names):
            elapsed_time = time.time() - start_time
            if elapsed_time > timeout_seconds:
                print(f"    ⚠️ 因子-行业R²计算超时 ({elapsed_time:.1f}s > {timeout_seconds}s)")
                break
                
            print(f"  计算 {factor_name} 对各行业的解释力...")
            factor_series = factors_std.iloc[:, factor_idx]
            industry_r2_for_factor = {}
            
            for industry, vars_in_industry in industry_groups.items():
                # 获取该行业的变量数据
                industry_data = data_processed[vars_in_industry].dropna(how='all')
                if industry_data.empty:
                    continue
                
                # 计算行业均值
                industry_mean = industry_data.mean(axis=1).dropna()
                
                # 与因子对齐
                aligned_data = pd.concat([industry_mean, factor_series], axis=1).dropna()
                if len(aligned_data) < 2:
                    continue
                
                Y = aligned_data.iloc[:, 0]  # 行业均值
                X = aligned_data.iloc[:, 1]  # 单个因子
                
                # 添加常数项并回归
                X_with_const = sm.add_constant(X)
                try:
                    ols_model = sm.OLS(Y, X_with_const)
                    ols_results = ols_model.fit()
                    industry_r2_for_factor[industry] = ols_results.rsquared
                except:
                    industry_r2_for_factor[industry] = 0.0
            
            if industry_r2_for_factor:
                factor_industry_r2[factor_name] = pd.Series(industry_r2_for_factor).sort_values(ascending=False)
        
        if factor_industry_r2:
            print(f"  ✅ 因子-行业R²计算完成，共 {len(factor_industry_r2)} 个因子")
            return factor_industry_r2
        else:
            print("  ⚠️ 没有成功计算任何因子-行业R²")
            return None
            
    except Exception as e:
        print(f"  计算因子-行业R²时发生错误: {e}")
        return None

# --- <<< 新增：计算单因子对变量类型的汇总 R2 >>> ---
def calculate_factor_type_r2(
    dfm_results: Any,
    data_processed: pd.DataFrame,
    variable_list: List[str],
    var_type_map: Dict[str, str], # <-- 使用类型映射
    n_factors: int,
    timeout_seconds: int = None  # 使用配置值
) -> Optional[Dict[str, pd.Series]]:
    """
    计算每个因子对各类型的R²。

    Args:
        dfm_results: DFM模型结果
        data_processed: 处理后的数据
        variable_list: 变量列表  
        var_type_map: 变量到类型的映射
        n_factors: 因子数量
        timeout_seconds: 超时时间（秒），使用配置默认值

    Returns:
        字典，键为因子名，值为类型R²的Series，失败时返回None
    """
    # 使用配置的超时值
    if timeout_seconds is None:
        timeout_seconds = PerformanceDefaults.TIMEOUT_MEDIUM if _CONFIG_AVAILABLE else 120
        
    print("\n计算各因子对变量类型的解释力...")
    start_time = time.time()
    
    try:
        # 验证输入
        if not (dfm_results and hasattr(dfm_results, 'x_sm')):
            print("  错误: DFM结果无效")
            return None
        
        factors_std = dfm_results.x_sm.iloc[:, :n_factors]
        factor_names = [f'Factor{i+1}' for i in range(n_factors)]
        
        # 按类型分组变量
        type_groups = {}
        for var in variable_list:
            if var in var_type_map:
                var_type = var_type_map[var]
                if var_type not in type_groups:
                    type_groups[var_type] = []
                type_groups[var_type].append(var)
        
        if not type_groups:
            print("  错误: 没有找到有效的类型分组")
            return None
        
        factor_type_r2 = {}
        
        for factor_idx, factor_name in enumerate(factor_names):
            elapsed_time = time.time() - start_time
            if elapsed_time > timeout_seconds:
                print(f"    ⚠️ 因子-类型R²计算超时 ({elapsed_time:.1f}s > {timeout_seconds}s)")
                break
                
            print(f"  计算 {factor_name} 对各类型的解释力...")
            factor_series = factors_std.iloc[:, factor_idx]
            type_r2_for_factor = {}
            
            for var_type, vars_in_type in type_groups.items():
                # 获取该类型的变量数据
                type_data = data_processed[vars_in_type].dropna(how='all')
                if type_data.empty:
                    continue
                
                # 计算类型均值
                type_mean = type_data.mean(axis=1).dropna()
                
                # 与因子对齐
                aligned_data = pd.concat([type_mean, factor_series], axis=1).dropna()
                if len(aligned_data) < 2:
                    continue
                
                Y = aligned_data.iloc[:, 0]  # 类型均值
                X = aligned_data.iloc[:, 1]  # 单个因子
                
                # 添加常数项并回归
                X_with_const = sm.add_constant(X)
                try:
                    ols_model = sm.OLS(Y, X_with_const)
                    ols_results = ols_model.fit()
                    type_r2_for_factor[var_type] = ols_results.rsquared
                except:
                    type_r2_for_factor[var_type] = 0.0
            
            if type_r2_for_factor:
                factor_type_r2[factor_name] = pd.Series(type_r2_for_factor).sort_values(ascending=False)
        
        if factor_type_r2:
            print(f"  ✅ 因子-类型R²计算完成，共 {len(factor_type_r2)} 个因子")
            return factor_type_r2
        else:
            print("  ⚠️ 没有成功计算任何因子-类型R²")
            return None
            
    except Exception as e:
        print(f"  计算因子-类型R²时发生错误: {e}")
        return None
# --- 结束修改 ---

# --- 结束新增函数 --- 