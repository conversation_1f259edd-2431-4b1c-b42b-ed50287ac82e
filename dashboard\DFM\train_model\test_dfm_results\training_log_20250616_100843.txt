=== 🚀 启动DFM训练管道 ===
目标变量: 工业增加值同比增速_月度_同花顺
选择的指标数量: 76
🔥 因子选择范围: (1, 20)
🔥 启用变量选择: True
🔥 启用超参数调优: False

=== 📋 参数传递验证 ===
📅 时间参数:
  - 训练开始日期: 2020-01-03
  - 训练结束日期: None
  - 验证开始日期: 2024-01-05
  - 验证结束日期: 2024-12-31
🎯 模型参数:
  - 因子数量: 3
  - 因子阶数: 1
  - 特异性自回归阶数: 1
  - EM最大迭代次数: 50
🔧 优化参数:
  - 变量选择方法: global_backward
  - 信息准则方法: bic
  - 累积方差阈值: 0.8
  - 使用Bai-Ng因子选择: True
📊 数据映射:
  - 行业映射对象: 已提供
  - 变量类型映射: 未提供
    行业映射数量: 76
📁 输出设置:
  - 输出基础目录: test_outputs
  - 启用详细分析: True
  - 生成Excel报告: True

=== ⚠️ 关键诊断信息 ===
🔍 _MODULES_AVAILABLE 状态: True
🔍 输入数据形状: (262, 76)
🔍 选择指标列表: ['规模以上工业增加值:当月同比', '中国:日均产量:生铁:重点企业', '中国:日均产量:粗钢:重点企业', '重点电厂:日耗量:煤炭', '中国:主要45港口:日均疏港量:铁矿石']...（前5个）
🔍 模块导入状态:
    - dfm_core: ✅ 成功
    - DynamicFactorModel: ✅ 成功
    - analysis_utils: ✅ 成功
    - variable_selection: ✅ 成功
    - results_analysis: ✅ 成功
步骤1: 数据准备...
使用传入的input_df进行训练
🔍🔍🔍 关键变量选择诊断:
    - 原始selected_indicators长度: 76
    - 目标变量: 工业增加值同比增速_月度_同花顺
    - 最终available_cols长度: 77
    - available_cols内容: ['工业增加值同比增速_月度_同花顺', '规模以上工业增加值:当月同比', '中国:日均产量:生铁:重点企业', '中国:日均产量:粗钢:重点企业', '重点电厂:日耗量:煤炭', '中国:主要45港口:日均疏港量:铁矿石', '中国:生产率:焦炉:国内独立焦化厂(230家)', '中国:高炉开工率(247家)', '中国:产量:螺纹钢:主要钢厂', '中国:开工率:线材:主要钢厂', '中国:产量:线材:主要钢厂', '中国:产量:冷轧板卷:主要钢厂', '中国:产量:热轧板卷:主要钢厂', '中国:产量:中厚板:主要钢厂', '中国:产量:钢材:重点钢铁企业', '中国:开工率:产能(>200万吨):焦化企业(230家)', '中国:开工率:产能(100-200万吨):焦化企业(230家)', '中国:主流港口:库存量:煤炭', '中国:库存量:焦炭:国内样本钢厂(247家)', '动力煤：462家样本矿山：产能利用率（周）', '动力煤：462家样本矿山：日均产量（周）', '精煤：样本洗煤厂（110家）：日均产量：中国（周）', '煤炭：样本洗煤厂（110家）：开工率：中国（周）', '精煤：523家样本矿山：日均产量（周）', '原煤：523家样本矿山：日均产量（周）', '炼焦煤：523家样本矿山：开工率（周）', '焦炭：230家独立焦化厂：产能利用率：中国（周）', '焦炭：230家独立焦化厂：日均产量：中国（周）', '中国:开工率(常减压开工率):山东地炼厂', '中国:产能利用率:成品油:独立炼厂', '中国:产能利用率:成品油:主营炼厂', '乙烯：MTO：生产企业：产能利用率：中国（周）', '乙烯：轻烃裂解：生产企业：产能利用率：中国（周）', '乙烯：石脑油裂解：生产企业：产能利用率：中国（周）', '乙烯：MTO：产量：中国（周）', '乙烯：产量：中国（周）', '乙烯：轻烃裂解：产量：中国（周）', '乙烯：石脑油裂解：产量：中国（周）', '乙烯：市场价：华东地区（周）', '氯化铵：产量：中国（周）', '氯化铵：产能利用率：中国（周）', '三聚氰胺：产量：中国（周）', '三聚氰胺：产能利用率：中国（周）', '尿素：产量：中国（周）', '尿素：产能利用率：中国（周）', '磷酸一铵：工业级：产能利用率：中国（周）', '磷酸一铵：工业级：产量：中国（周）', '磷酸一铵：产量：中国（周）', '磷酸二铵：产量：中国（周）', '硫酸钾：开工率：中国（周）', '氯化钾：产能利用率：中国（周）', '中国:开工率:精对苯二甲酸', '中国:江浙地区:开工率:涤纶长丝', '中国:装置负荷率:涤纶短纤', 'PTA：产能利用率：中国（周）', 'PTA：产量：中国（周）', 'MEG：产能利用率：中国（周）', 'MEG：产量：中国（周）', '聚酯：产能利用率：中国（周）', '聚酯：产量：中国（周）', 'PE：产能利用率：中国（周）', 'PE：化工生产企业：产量：中国（周）', 'PE：社会库存：中国（周）', 'PP：注塑：开工率：中国（周）', 'PP：产能利用率：中国（周）', 'PP：产量：中国（周）', 'PVC：产能利用率：中国（周）', 'PVC：产量：中国（周）', '中国:开工率:汽车轮胎(半钢胎)', '中国:开工率:汽车轮胎(全钢胎)', '制造业PMI', '制造业PMI:生产', '制造业PMI:新订单', '制造业PMI:新出口订单', '制造业PMI:从业人员', '中国：可再生能源：发电量（月）', '中国：火力发电：发电量（月）']
数据子集形状: (262, 76)
时间范围: 2019-12-27 00:00:00 到 2024-12-27 00:00:00
时间过滤后数据形状: (261, 76)
自动计算训练结束日期: 2023-12-29 00:00:00
步骤2: 数据标准化...
步骤3: 因子选择...
🔥 选择的因子数量: 3
步骤4: 变量选择...
🔥 开始简化变量选择，初始变量数: 76
   目标变量: 工业增加值同比增速_月度_同花顺
   预测变量数: 75
🔍 检查目标变量在数据中的存在性:
   目标变量: 工业增加值同比增速_月度_同花顺
   数据列: ['规模以上工业增加值:当月同比', '中国:日均产量:生铁:重点企业', '中国:日均产量:粗钢:重点企业', '重点电厂:日耗量:煤炭', '中国:主要45港口:日均疏港量:铁矿石', '中国:生产率:焦炉:国内独立焦化厂(230家)', '中国:高炉开工率(247家)', '中国:产量:螺纹钢:主要钢厂', '中国:开工率:线材:主要钢厂', '中国:产量:线材:主要钢厂']...
   目标变量是否在数据中: False
🔧 变量数量较多，执行基于相关性的筛选...
❌ 目标变量 工业增加值同比增速_月度_同花顺 不在标准化数据中，跳过相关性分析
⚠️ 无相关性分析，选择前20个变量
✅ 简化变量选择完成!
   最终选择的变量数: 21
   其中预测变量数: 20
步骤5: 训练最终DFM模型...
DFM训练失败: "['工业增加值同比增速_月度_同花顺'] not in index"
步骤6: 保存结果...
🔍 结果保存目录: test_outputs
🔍 目录是否存在: True
🔍 DFM结果是否存在: False
🔍 开始保存过程...
🔍 尝试保存模型到: test_outputs\final_dfm_model.joblib
🔍 保存占位符模型...
⚠️ 简化模型保存成功: test_outputs\final_dfm_model.joblib
    文件大小: 1073 bytes
🔍 尝试保存元数据到: test_outputs\final_dfm_metadata.pkl
✅ 元数据保存成功: test_outputs\final_dfm_metadata.pkl
    文件大小: 1500 bytes
🔍 尝试保存训练数据到: test_outputs\training_data.csv
❌ 训练数据保存失败: "['工业增加值同比增速_月度_同花顺'] not in index"
详细错误: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\HFTA0610_test\dashboard\DFM\train_model\tune_dfm.py", line 751, in train_and_save_dfm_results
    training_data_subset = data_standardized[final_variables].copy()
                           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\pandas\core\frame.py", line 4113, in __getitem__
    indexer = self.columns._get_indexer_strict(key, "columns")[1]
              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\pandas\core\indexes\base.py", line 6212, in _get_indexer_strict
    self._raise_if_missing(keyarr, indexer, axis_name)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\pandas\core\indexes\base.py", line 6264, in _raise_if_missing
    raise KeyError(f"{not_found} not in index")
KeyError: "['工业增加值同比增速_月度_同花顺'] not in index"

步骤7: 生成Excel报告...
🔍 尝试生成Excel报告到: test_outputs\comprehensive_dfm_report.xlsx
❌ Excel报告生成失败: "['工业增加值同比增速_月度_同花顺'] not in index"
详细错误: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\HFTA0610_test\dashboard\DFM\train_model\tune_dfm.py", line 791, in train_and_save_dfm_results
    preview_data = data_standardized[final_variables].head(10)
                   ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\pandas\core\frame.py", line 4113, in __getitem__
    indexer = self.columns._get_indexer_strict(key, "columns")[1]
              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\pandas\core\indexes\base.py", line 6212, in _get_indexer_strict
    self._raise_if_missing(keyarr, indexer, axis_name)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\pandas\core\indexes\base.py", line 6264, in _raise_if_missing
    raise KeyError(f"{not_found} not in index")
KeyError: "['工业增加值同比增速_月度_同花顺'] not in index"

=== ✅ DFM训练管道完成 ===
生成文件数量: 2
🔍🔍🔍 详细文件列表:
    📁 final_model_joblib: test_outputs\final_dfm_model.joblib
       - 存在: ✅
       - 大小: 1073 bytes
    📁 metadata: test_outputs\final_dfm_metadata.pkl
       - 存在: ✅
       - 大小: 1500 bytes

🔍🔍🔥 强制检查结果目录内容:
    实际目录中的文件数量: 4
    📄 comprehensive_dfm_report.xlsx (6539 bytes)
    📄 final_dfm_metadata.pkl (1500 bytes)
    📄 final_dfm_model.joblib (1073 bytes)
    📄 training_data.csv (324582 bytes)
🔍🔍🔥 saved_files字典内容检查:
    字典长度: 2
    字典键: ['final_model_joblib', 'metadata']
    字典值: ['test_outputs\\final_dfm_model.joblib', 'test_outputs\\final_dfm_metadata.pkl']
