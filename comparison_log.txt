2025-06-16 10:12:33: 🚀 开始DFM老代码与新代码比较分析
2025-06-16 10:12:33: ============================================================
2025-06-16 10:12:33: 🔍 开始分析老代码结构...
2025-06-16 10:12:33:   ✅ 分析文件: tune_dfm.py (1987 行)
2025-06-16 10:12:33:   ✅ 分析文件: dfm_core.py (393 行)
2025-06-16 10:12:33:   ✅ 分析文件: data_preparation.py (1747 行)
2025-06-16 10:12:33:   ✅ 分析文件: variable_selection.py (563 行)
2025-06-16 10:12:33:   ✅ 分析文件: DynamicFactorModel.py (340 行)
2025-06-16 10:12:33:   ✅ 分析文件: config.py (112 行)
2025-06-16 10:12:33:   🔍 分析老代码主流程...
2025-06-16 10:12:33: 🚀 开始运行新代码测试...
2025-06-16 10:12:36:   📊 执行新代码数据准备...
2025-06-16 10:12:37:   ❌ 新代码测试出错: too many values to unpack (expected 2)
2025-06-16 10:12:37:   详细错误: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\HFTA0610_test\compare_old_new_dfm.py", line 123, in run_new_code_test
    prepared_data, var_industry_map = prepare_data(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ValueError: too many values to unpack (expected 2)

2025-06-16 10:12:37: 🔍 模拟老代码预期结果...
2025-06-16 10:12:37:   ✅ 老代码结果模拟完成
2025-06-16 10:12:37: 📊 开始比较分析...
2025-06-16 10:12:37:   🔍 比较代码结构...
2025-06-16 10:12:37:   🔍 比较功能实现...
2025-06-16 10:12:37:   🔍 比较数据处理...
2025-06-16 10:12:37:   🔍 比较输出结果...
2025-06-16 10:12:37: 📝 生成比较报告...
2025-06-16 10:12:37:   ✅ 比较报告已保存: dfm_comparison_report.txt
2025-06-16 10:12:37: ============================================================
2025-06-16 10:12:37: 🎉 比较分析完成!
2025-06-16 10:12:37: 📊 详细报告: dfm_comparison_report.txt
2025-06-16 10:12:37: 📝 执行日志: comparison_log.txt
2025-06-16 10:12:37: ⚠️ 新代码测试有问题
2025-06-16 10:12:37: 🔍 主要发现:
2025-06-16 10:12:37:   - 老代码使用复杂的两阶段优化流程
2025-06-16 10:12:37:   - 新代码采用简化的模块化设计
2025-06-16 10:12:37:   - 变量选择方法有显著差异
2025-06-16 10:12:37:   - 因子选择使用不同的理论方法
2025-06-16 10:12:40: 🚀 开始DFM老代码与新代码比较分析
2025-06-16 10:12:40: ============================================================
2025-06-16 10:12:40: 🔍 开始分析老代码结构...
2025-06-16 10:12:40:   ✅ 分析文件: tune_dfm.py (1987 行)
2025-06-16 10:12:40:   ✅ 分析文件: dfm_core.py (393 行)
2025-06-16 10:12:40:   ✅ 分析文件: data_preparation.py (1747 行)
2025-06-16 10:12:40:   ✅ 分析文件: variable_selection.py (563 行)
2025-06-16 10:12:40:   ✅ 分析文件: DynamicFactorModel.py (340 行)
2025-06-16 10:12:40:   ✅ 分析文件: config.py (112 行)
2025-06-16 10:12:40:   🔍 分析老代码主流程...
2025-06-16 10:12:40: 🚀 开始运行新代码测试...
2025-06-16 10:12:42:   📊 执行新代码数据准备...
2025-06-16 10:12:43:   ❌ 新代码测试出错: too many values to unpack (expected 2)
2025-06-16 10:12:43:   详细错误: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\HFTA0610_test\compare_old_new_dfm.py", line 123, in run_new_code_test
    prepared_data, var_industry_map = prepare_data(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ValueError: too many values to unpack (expected 2)

2025-06-16 10:12:43: 🔍 模拟老代码预期结果...
2025-06-16 10:12:43:   ✅ 老代码结果模拟完成
2025-06-16 10:12:43: 📊 开始比较分析...
2025-06-16 10:12:43:   🔍 比较代码结构...
2025-06-16 10:12:43:   🔍 比较功能实现...
2025-06-16 10:12:43:   🔍 比较数据处理...
2025-06-16 10:12:43:   🔍 比较输出结果...
2025-06-16 10:12:43: 📝 生成比较报告...
2025-06-16 10:12:43:   ✅ 比较报告已保存: dfm_comparison_report.txt
2025-06-16 10:12:43: ============================================================
2025-06-16 10:12:43: 🎉 比较分析完成!
2025-06-16 10:12:43: 📊 详细报告: dfm_comparison_report.txt
2025-06-16 10:12:43: 📝 执行日志: comparison_log.txt
2025-06-16 10:12:43: ⚠️ 新代码测试有问题
2025-06-16 10:12:43: 🔍 主要发现:
2025-06-16 10:12:43:   - 老代码使用复杂的两阶段优化流程
2025-06-16 10:12:43:   - 新代码采用简化的模块化设计
2025-06-16 10:12:43:   - 变量选择方法有显著差异
2025-06-16 10:12:43:   - 因子选择使用不同的理论方法
2025-06-16 10:12:46: 🚀 开始DFM老代码与新代码比较分析
2025-06-16 10:12:46: ============================================================
2025-06-16 10:12:46: 🔍 开始分析老代码结构...
2025-06-16 10:12:46:   ✅ 分析文件: tune_dfm.py (1987 行)
2025-06-16 10:12:46:   ✅ 分析文件: dfm_core.py (393 行)
2025-06-16 10:12:46:   ✅ 分析文件: data_preparation.py (1747 行)
2025-06-16 10:12:46:   ✅ 分析文件: variable_selection.py (563 行)
2025-06-16 10:12:46:   ✅ 分析文件: DynamicFactorModel.py (340 行)
2025-06-16 10:12:46:   ✅ 分析文件: config.py (112 行)
2025-06-16 10:12:46:   🔍 分析老代码主流程...
2025-06-16 10:12:46: 🚀 开始运行新代码测试...
2025-06-16 10:12:48:   📊 执行新代码数据准备...
2025-06-16 10:12:49:   ❌ 新代码测试出错: too many values to unpack (expected 2)
2025-06-16 10:12:49:   详细错误: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\HFTA0610_test\compare_old_new_dfm.py", line 123, in run_new_code_test
    prepared_data, var_industry_map = prepare_data(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ValueError: too many values to unpack (expected 2)

2025-06-16 10:12:49: 🔍 模拟老代码预期结果...
2025-06-16 10:12:49:   ✅ 老代码结果模拟完成
2025-06-16 10:12:49: 📊 开始比较分析...
2025-06-16 10:12:49:   🔍 比较代码结构...
2025-06-16 10:12:49:   🔍 比较功能实现...
2025-06-16 10:12:49:   🔍 比较数据处理...
2025-06-16 10:12:49:   🔍 比较输出结果...
2025-06-16 10:12:49: 📝 生成比较报告...
2025-06-16 10:12:49:   ✅ 比较报告已保存: dfm_comparison_report.txt
2025-06-16 10:12:49: ============================================================
2025-06-16 10:12:49: 🎉 比较分析完成!
2025-06-16 10:12:49: 📊 详细报告: dfm_comparison_report.txt
2025-06-16 10:12:49: 📝 执行日志: comparison_log.txt
2025-06-16 10:12:49: ⚠️ 新代码测试有问题
2025-06-16 10:12:49: 🔍 主要发现:
2025-06-16 10:12:49:   - 老代码使用复杂的两阶段优化流程
2025-06-16 10:12:49:   - 新代码采用简化的模块化设计
2025-06-16 10:12:49:   - 变量选择方法有显著差异
2025-06-16 10:12:49:   - 因子选择使用不同的理论方法
2025-06-16 10:12:52: 🚀 开始DFM老代码与新代码比较分析
2025-06-16 10:12:52: ============================================================
2025-06-16 10:12:52: 🔍 开始分析老代码结构...
2025-06-16 10:12:52:   ✅ 分析文件: tune_dfm.py (1987 行)
2025-06-16 10:12:52:   ✅ 分析文件: dfm_core.py (393 行)
2025-06-16 10:12:52:   ✅ 分析文件: data_preparation.py (1747 行)
2025-06-16 10:12:52:   ✅ 分析文件: variable_selection.py (563 行)
2025-06-16 10:12:52:   ✅ 分析文件: DynamicFactorModel.py (340 行)
2025-06-16 10:12:52:   ✅ 分析文件: config.py (112 行)
2025-06-16 10:12:52:   🔍 分析老代码主流程...
2025-06-16 10:12:52: 🚀 开始运行新代码测试...
2025-06-16 10:12:54:   📊 执行新代码数据准备...
2025-06-16 10:12:55:   ❌ 新代码测试出错: too many values to unpack (expected 2)
2025-06-16 10:12:55:   详细错误: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\HFTA0610_test\compare_old_new_dfm.py", line 123, in run_new_code_test
    prepared_data, var_industry_map = prepare_data(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ValueError: too many values to unpack (expected 2)

2025-06-16 10:12:55: 🔍 模拟老代码预期结果...
2025-06-16 10:12:55:   ✅ 老代码结果模拟完成
2025-06-16 10:12:55: 📊 开始比较分析...
2025-06-16 10:12:55:   🔍 比较代码结构...
2025-06-16 10:12:55:   🔍 比较功能实现...
2025-06-16 10:12:55:   🔍 比较数据处理...
2025-06-16 10:12:55:   🔍 比较输出结果...
2025-06-16 10:12:55: 📝 生成比较报告...
2025-06-16 10:12:55:   ✅ 比较报告已保存: dfm_comparison_report.txt
2025-06-16 10:12:55: ============================================================
2025-06-16 10:12:55: 🎉 比较分析完成!
2025-06-16 10:12:55: 📊 详细报告: dfm_comparison_report.txt
2025-06-16 10:12:55: 📝 执行日志: comparison_log.txt
2025-06-16 10:12:55: ⚠️ 新代码测试有问题
2025-06-16 10:12:55: 🔍 主要发现:
2025-06-16 10:12:55:   - 老代码使用复杂的两阶段优化流程
2025-06-16 10:12:55:   - 新代码采用简化的模块化设计
2025-06-16 10:12:55:   - 变量选择方法有显著差异
2025-06-16 10:12:55:   - 因子选择使用不同的理论方法
2025-06-16 10:12:58: 🚀 开始DFM老代码与新代码比较分析
2025-06-16 10:12:58: ============================================================
2025-06-16 10:12:58: 🔍 开始分析老代码结构...
2025-06-16 10:12:58:   ✅ 分析文件: tune_dfm.py (1987 行)
2025-06-16 10:12:58:   ✅ 分析文件: dfm_core.py (393 行)
2025-06-16 10:12:58:   ✅ 分析文件: data_preparation.py (1747 行)
2025-06-16 10:12:58:   ✅ 分析文件: variable_selection.py (563 行)
2025-06-16 10:12:58:   ✅ 分析文件: DynamicFactorModel.py (340 行)
2025-06-16 10:12:58:   ✅ 分析文件: config.py (112 行)
2025-06-16 10:12:58:   🔍 分析老代码主流程...
2025-06-16 10:12:58: 🚀 开始运行新代码测试...
2025-06-16 10:13:00:   📊 执行新代码数据准备...
2025-06-16 10:13:01:   ❌ 新代码测试出错: too many values to unpack (expected 2)
2025-06-16 10:13:01:   详细错误: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\HFTA0610_test\compare_old_new_dfm.py", line 123, in run_new_code_test
    prepared_data, var_industry_map = prepare_data(
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ValueError: too many values to unpack (expected 2)

2025-06-16 10:13:01: 🔍 模拟老代码预期结果...
2025-06-16 10:13:01:   ✅ 老代码结果模拟完成
2025-06-16 10:13:01: 📊 开始比较分析...
2025-06-16 10:13:01:   🔍 比较代码结构...
2025-06-16 10:13:01:   🔍 比较功能实现...
2025-06-16 10:13:01:   🔍 比较数据处理...
2025-06-16 10:13:01:   🔍 比较输出结果...
2025-06-16 10:13:01: 📝 生成比较报告...
2025-06-16 10:13:01:   ✅ 比较报告已保存: dfm_comparison_report.txt
2025-06-16 10:13:01: ============================================================
2025-06-16 10:13:01: 🎉 比较分析完成!
2025-06-16 10:13:01: 📊 详细报告: dfm_comparison_report.txt
2025-06-16 10:13:01: 📝 执行日志: comparison_log.txt
2025-06-16 10:13:01: ⚠️ 新代码测试有问题
2025-06-16 10:13:01: 🔍 主要发现:
2025-06-16 10:13:01:   - 老代码使用复杂的两阶段优化流程
2025-06-16 10:13:01:   - 新代码采用简化的模块化设计
2025-06-16 10:13:01:   - 变量选择方法有显著差异
2025-06-16 10:13:01:   - 因子选择使用不同的理论方法
