--- 开始详细调优日志 (Run: 20250616_105030) ---
输出目录: dym_estimate\dfm_result
配置: 两阶段流程
  阶段1: 全局后向变量筛选 (固定 k=块数 N, 优化 HR -> -RMSE)
  阶段2: 因子选择 (方法=bai_ng, 阈值=Drop<0.1)
*** 完整模式：使用所有变量进行筛选 ***

--- 全局后向筛选开始 ---
初始预测变量数: 75
初始基准得分 (HR, -RMSE): (np.float64(75.6081081081081), np.float64(-1.2658256747965506))
Iter 1: 移除 'PTA：产量：中国（周）'，得分 (HR=75.61%, RMSE=1.265826) -> (HR=79.26%, RMSE=1.219749)
Iter 2: 移除 '精煤：样本洗煤厂（110家）：日均产量：中国（周）'，得分 (HR=79.26%, RMSE=1.219749) -> (HR=81.96%, RMSE=1.272248)
Iter 3: 移除 '硫酸钾：开工率：中国（周）'，得分 (HR=81.96%, RMSE=1.272248) -> (HR=82.91%, RMSE=1.306743)
Iter 4: 移除 '制造业PMI:生产'，得分 (HR=82.91%, RMSE=1.306743) -> (HR=84.05%, RMSE=1.301562)
Iter 5: 未找到更优移除，筛选结束。

--- 全局后向筛选结束 ---
最终预测变量数: 71
最终得分 (HR, -RMSE): (np.float64(84.05405405405406), np.float64(-1.3015620522154911))

--- 阶段 1 结果 (全局筛选) ---
起始变量范围: 全部 76 个变量
固定因子数 (N): 10
最佳评分 (HR, -RMSE): (np.float64(84.05405405405406), np.float64(-1.3015620522154911))
最终预测变量数量: 71

--- 阶段 2 结果 ---
因子选择方法: bai_ng (基于 PCA)
最终选择因子数: 9
(np.float64(84.05405405405406), np.float64(-1.3015620522154911))
检查并转换 DFM 结果对象中的 Factors 和 Loadings...
  Factors (x_sm) 已是 DataFrame (Shape: (285, 9))，列名符合预期。
  Loadings (Lambda) 已从 NumPy 转换为 DataFrame (Shape: (72, 9))。
开始计算行业 Pooled R²...
  处理行业: '化纤' (8 个变量)...
  行业 '化纤' Pooled R²: 0.7798 (基于 8 个变量)
  处理行业: '橡胶塑料' (8 个变量)...
  行业 '橡胶塑料' Pooled R²: 0.8022 (基于 8 个变量)
  处理行业: '化学化工' (19 个变量)...
  行业 '化学化工' Pooled R²: 0.4503 (基于 19 个变量)
  处理行业: '煤炭' (12 个变量)...
  行业 '煤炭' Pooled R²: 0.9729 (基于 12 个变量)
  处理行业: '钢铁' (12 个变量)...
  行业 '钢铁' Pooled R²: 0.4171 (基于 12 个变量)
  处理行业: '油气' (3 个变量)...
  行业 '油气' Pooled R²: 0.9508 (基于 3 个变量)
  处理行业: '汽车' (2 个变量)...
  行业 '汽车' Pooled R²: 0.7557 (基于 2 个变量)
  处理行业: '电力' (3 个变量)...
  行业 '电力' Pooled R²: 0.2215 (基于 3 个变量)
  处理行业: '制造业' (4 个变量)...
  行业 '制造业' Pooled R²: 0.4802 (基于 4 个变量)
  处理行业: '工业' (1 个变量)...
  行业 '工业' Pooled R²: 0.6181 (基于 1 个变量)
开始计算单因子对行业的 Pooled R²...
--- 计算因子: Factor1 ---
  处理行业: '化纤' (8 个变量) 对 Factor1...
  => Factor1 对行业 '化纤' 的 Pooled R²: 0.3255 (基于 8 个变量)
  处理行业: '橡胶塑料' (8 个变量) 对 Factor1...
  => Factor1 对行业 '橡胶塑料' 的 Pooled R²: 0.0642 (基于 8 个变量)
  处理行业: '化学化工' (19 个变量) 对 Factor1...
  => Factor1 对行业 '化学化工' 的 Pooled R²: 0.0335 (基于 19 个变量)
  处理行业: '煤炭' (12 个变量) 对 Factor1...
  => Factor1 对行业 '煤炭' 的 Pooled R²: 0.1251 (基于 12 个变量)
  处理行业: '钢铁' (12 个变量) 对 Factor1...
  => Factor1 对行业 '钢铁' 的 Pooled R²: 0.2332 (基于 12 个变量)
  处理行业: '油气' (3 个变量) 对 Factor1...
  => Factor1 对行业 '油气' 的 Pooled R²: 0.0735 (基于 3 个变量)
  处理行业: '汽车' (2 个变量) 对 Factor1...
  => Factor1 对行业 '汽车' 的 Pooled R²: 0.1002 (基于 2 个变量)
  处理行业: '电力' (3 个变量) 对 Factor1...
  => Factor1 对行业 '电力' 的 Pooled R²: 0.0257 (基于 3 个变量)
  处理行业: '制造业' (4 个变量) 对 Factor1...
  => Factor1 对行业 '制造业' 的 Pooled R²: 0.0962 (基于 4 个变量)
  处理行业: '工业' (1 个变量) 对 Factor1...
  => Factor1 对行业 '工业' 的 Pooled R²: 0.1731 (基于 1 个变量)
--- 计算因子: Factor2 ---
  处理行业: '化纤' (8 个变量) 对 Factor2...
  => Factor2 对行业 '化纤' 的 Pooled R²: 0.0697 (基于 8 个变量)
  处理行业: '橡胶塑料' (8 个变量) 对 Factor2...
  => Factor2 对行业 '橡胶塑料' 的 Pooled R²: 0.1043 (基于 8 个变量)
  处理行业: '化学化工' (19 个变量) 对 Factor2...
  => Factor2 对行业 '化学化工' 的 Pooled R²: 0.1016 (基于 19 个变量)
  处理行业: '煤炭' (12 个变量) 对 Factor2...
  => Factor2 对行业 '煤炭' 的 Pooled R²: 0.6966 (基于 12 个变量)
  处理行业: '钢铁' (12 个变量) 对 Factor2...
  => Factor2 对行业 '钢铁' 的 Pooled R²: 0.0071 (基于 12 个变量)
  处理行业: '油气' (3 个变量) 对 Factor2...
  => Factor2 对行业 '油气' 的 Pooled R²: 0.0216 (基于 3 个变量)
  处理行业: '汽车' (2 个变量) 对 Factor2...
  => Factor2 对行业 '汽车' 的 Pooled R²: 0.0424 (基于 2 个变量)
  处理行业: '电力' (3 个变量) 对 Factor2...
  => Factor2 对行业 '电力' 的 Pooled R²: 0.0115 (基于 3 个变量)
  处理行业: '制造业' (4 个变量) 对 Factor2...
  => Factor2 对行业 '制造业' 的 Pooled R²: 0.1556 (基于 4 个变量)
  处理行业: '工业' (1 个变量) 对 Factor2...
  => Factor2 对行业 '工业' 的 Pooled R²: 0.2072 (基于 1 个变量)
--- 计算因子: Factor3 ---
  处理行业: '化纤' (8 个变量) 对 Factor3...
  => Factor3 对行业 '化纤' 的 Pooled R²: 0.0977 (基于 8 个变量)
  处理行业: '橡胶塑料' (8 个变量) 对 Factor3...
  => Factor3 对行业 '橡胶塑料' 的 Pooled R²: 0.2455 (基于 8 个变量)
  处理行业: '化学化工' (19 个变量) 对 Factor3...
  => Factor3 对行业 '化学化工' 的 Pooled R²: 0.0323 (基于 19 个变量)
  处理行业: '煤炭' (12 个变量) 对 Factor3...
  => Factor3 对行业 '煤炭' 的 Pooled R²: 0.0333 (基于 12 个变量)
  处理行业: '钢铁' (12 个变量) 对 Factor3...
  => Factor3 对行业 '钢铁' 的 Pooled R²: 0.0063 (基于 12 个变量)
  处理行业: '油气' (3 个变量) 对 Factor3...
  => Factor3 对行业 '油气' 的 Pooled R²: 0.6731 (基于 3 个变量)
  处理行业: '汽车' (2 个变量) 对 Factor3...
  => Factor3 对行业 '汽车' 的 Pooled R²: 0.1112 (基于 2 个变量)
  处理行业: '电力' (3 个变量) 对 Factor3...
  => Factor3 对行业 '电力' 的 Pooled R²: 0.0445 (基于 3 个变量)
  处理行业: '制造业' (4 个变量) 对 Factor3...
  => Factor3 对行业 '制造业' 的 Pooled R²: 0.0119 (基于 4 个变量)
  处理行业: '工业' (1 个变量) 对 Factor3...
  => Factor3 对行业 '工业' 的 Pooled R²: 0.0004 (基于 1 个变量)
--- 计算因子: Factor4 ---
  处理行业: '化纤' (8 个变量) 对 Factor4...
  => Factor4 对行业 '化纤' 的 Pooled R²: 0.0208 (基于 8 个变量)
  处理行业: '橡胶塑料' (8 个变量) 对 Factor4...
  => Factor4 对行业 '橡胶塑料' 的 Pooled R²: 0.0250 (基于 8 个变量)
  处理行业: '化学化工' (19 个变量) 对 Factor4...
  => Factor4 对行业 '化学化工' 的 Pooled R²: 0.0251 (基于 19 个变量)
  处理行业: '煤炭' (12 个变量) 对 Factor4...
  => Factor4 对行业 '煤炭' 的 Pooled R²: 0.0049 (基于 12 个变量)
  处理行业: '钢铁' (12 个变量) 对 Factor4...
  => Factor4 对行业 '钢铁' 的 Pooled R²: 0.0197 (基于 12 个变量)
  处理行业: '油气' (3 个变量) 对 Factor4...
  => Factor4 对行业 '油气' 的 Pooled R²: 0.0236 (基于 3 个变量)
  处理行业: '汽车' (2 个变量) 对 Factor4...
  => Factor4 对行业 '汽车' 的 Pooled R²: 0.0022 (基于 2 个变量)
  处理行业: '电力' (3 个变量) 对 Factor4...
  => Factor4 对行业 '电力' 的 Pooled R²: 0.0019 (基于 3 个变量)
  处理行业: '制造业' (4 个变量) 对 Factor4...
  => Factor4 对行业 '制造业' 的 Pooled R²: 0.0762 (基于 4 个变量)
  处理行业: '工业' (1 个变量) 对 Factor4...
  => Factor4 对行业 '工业' 的 Pooled R²: 0.0715 (基于 1 个变量)
--- 计算因子: Factor5 ---
  处理行业: '化纤' (8 个变量) 对 Factor5...
  => Factor5 对行业 '化纤' 的 Pooled R²: 0.0352 (基于 8 个变量)
  处理行业: '橡胶塑料' (8 个变量) 对 Factor5...
  => Factor5 对行业 '橡胶塑料' 的 Pooled R²: 0.0168 (基于 8 个变量)
  处理行业: '化学化工' (19 个变量) 对 Factor5...
  => Factor5 对行业 '化学化工' 的 Pooled R²: 0.0197 (基于 19 个变量)
  处理行业: '煤炭' (12 个变量) 对 Factor5...
  => Factor5 对行业 '煤炭' 的 Pooled R²: 0.0244 (基于 12 个变量)
  处理行业: '钢铁' (12 个变量) 对 Factor5...
  => Factor5 对行业 '钢铁' 的 Pooled R²: 0.0628 (基于 12 个变量)
  处理行业: '油气' (3 个变量) 对 Factor5...
  => Factor5 对行业 '油气' 的 Pooled R²: 0.0611 (基于 3 个变量)
  处理行业: '汽车' (2 个变量) 对 Factor5...
  => Factor5 对行业 '汽车' 的 Pooled R²: 0.0524 (基于 2 个变量)
  处理行业: '电力' (3 个变量) 对 Factor5...
  => Factor5 对行业 '电力' 的 Pooled R²: 0.0300 (基于 3 个变量)
  处理行业: '制造业' (4 个变量) 对 Factor5...
  => Factor5 对行业 '制造业' 的 Pooled R²: 0.0168 (基于 4 个变量)
  处理行业: '工业' (1 个变量) 对 Factor5...
  => Factor5 对行业 '工业' 的 Pooled R²: 0.0000 (基于 1 个变量)
--- 计算因子: Factor6 ---
  处理行业: '化纤' (8 个变量) 对 Factor6...
  => Factor6 对行业 '化纤' 的 Pooled R²: 0.0037 (基于 8 个变量)
  处理行业: '橡胶塑料' (8 个变量) 对 Factor6...
  => Factor6 对行业 '橡胶塑料' 的 Pooled R²: 0.0166 (基于 8 个变量)
  处理行业: '化学化工' (19 个变量) 对 Factor6...
  => Factor6 对行业 '化学化工' 的 Pooled R²: 0.0246 (基于 19 个变量)
  处理行业: '煤炭' (12 个变量) 对 Factor6...
  => Factor6 对行业 '煤炭' 的 Pooled R²: 0.1014 (基于 12 个变量)
  处理行业: '钢铁' (12 个变量) 对 Factor6...
  => Factor6 对行业 '钢铁' 的 Pooled R²: 0.0074 (基于 12 个变量)
  处理行业: '油气' (3 个变量) 对 Factor6...
  => Factor6 对行业 '油气' 的 Pooled R²: 0.0003 (基于 3 个变量)
  处理行业: '汽车' (2 个变量) 对 Factor6...
  => Factor6 对行业 '汽车' 的 Pooled R²: 0.0111 (基于 2 个变量)
  处理行业: '电力' (3 个变量) 对 Factor6...
  => Factor6 对行业 '电力' 的 Pooled R²: 0.0040 (基于 3 个变量)
  处理行业: '制造业' (4 个变量) 对 Factor6...
  => Factor6 对行业 '制造业' 的 Pooled R²: 0.0116 (基于 4 个变量)
  处理行业: '工业' (1 个变量) 对 Factor6...
  => Factor6 对行业 '工业' 的 Pooled R²: 0.1679 (基于 1 个变量)
--- 计算因子: Factor7 ---
  处理行业: '化纤' (8 个变量) 对 Factor7...
  => Factor7 对行业 '化纤' 的 Pooled R²: 0.0095 (基于 8 个变量)
  处理行业: '橡胶塑料' (8 个变量) 对 Factor7...
  => Factor7 对行业 '橡胶塑料' 的 Pooled R²: 0.1786 (基于 8 个变量)
  处理行业: '化学化工' (19 个变量) 对 Factor7...
  => Factor7 对行业 '化学化工' 的 Pooled R²: 0.0779 (基于 19 个变量)
  处理行业: '煤炭' (12 个变量) 对 Factor7...
  => Factor7 对行业 '煤炭' 的 Pooled R²: 0.0457 (基于 12 个变量)
  处理行业: '钢铁' (12 个变量) 对 Factor7...
  => Factor7 对行业 '钢铁' 的 Pooled R²: 0.0384 (基于 12 个变量)
  处理行业: '油气' (3 个变量) 对 Factor7...
  => Factor7 对行业 '油气' 的 Pooled R²: 0.0052 (基于 3 个变量)
  处理行业: '汽车' (2 个变量) 对 Factor7...
  => Factor7 对行业 '汽车' 的 Pooled R²: 0.2309 (基于 2 个变量)
  处理行业: '电力' (3 个变量) 对 Factor7...
  => Factor7 对行业 '电力' 的 Pooled R²: 0.0400 (基于 3 个变量)
  处理行业: '制造业' (4 个变量) 对 Factor7...
  => Factor7 对行业 '制造业' 的 Pooled R²: 0.0057 (基于 4 个变量)
  处理行业: '工业' (1 个变量) 对 Factor7...
  => Factor7 对行业 '工业' 的 Pooled R²: 0.0381 (基于 1 个变量)
--- 计算因子: Factor8 ---
  处理行业: '化纤' (8 个变量) 对 Factor8...
  => Factor8 对行业 '化纤' 的 Pooled R²: 0.0900 (基于 8 个变量)
  处理行业: '橡胶塑料' (8 个变量) 对 Factor8...
  => Factor8 对行业 '橡胶塑料' 的 Pooled R²: 0.0620 (基于 8 个变量)
  处理行业: '化学化工' (19 个变量) 对 Factor8...
  => Factor8 对行业 '化学化工' 的 Pooled R²: 0.0130 (基于 19 个变量)
  处理行业: '煤炭' (12 个变量) 对 Factor8...
  => Factor8 对行业 '煤炭' 的 Pooled R²: 0.2617 (基于 12 个变量)
  处理行业: '钢铁' (12 个变量) 对 Factor8...
  => Factor8 对行业 '钢铁' 的 Pooled R²: 0.0628 (基于 12 个变量)
  处理行业: '油气' (3 个变量) 对 Factor8...
  => Factor8 对行业 '油气' 的 Pooled R²: 0.0723 (基于 3 个变量)
  处理行业: '汽车' (2 个变量) 对 Factor8...
  => Factor8 对行业 '汽车' 的 Pooled R²: 0.0616 (基于 2 个变量)
  处理行业: '电力' (3 个变量) 对 Factor8...
  => Factor8 对行业 '电力' 的 Pooled R²: 0.0258 (基于 3 个变量)
  处理行业: '制造业' (4 个变量) 对 Factor8...
  => Factor8 对行业 '制造业' 的 Pooled R²: 0.1669 (基于 4 个变量)
  处理行业: '工业' (1 个变量) 对 Factor8...
  => Factor8 对行业 '工业' 的 Pooled R²: 0.0129 (基于 1 个变量)
--- 计算因子: Factor9 ---
  处理行业: '化纤' (8 个变量) 对 Factor9...
  => Factor9 对行业 '化纤' 的 Pooled R²: 0.0028 (基于 8 个变量)
  处理行业: '橡胶塑料' (8 个变量) 对 Factor9...
  => Factor9 对行业 '橡胶塑料' 的 Pooled R²: 0.0216 (基于 8 个变量)
  处理行业: '化学化工' (19 个变量) 对 Factor9...
  => Factor9 对行业 '化学化工' 的 Pooled R²: 0.0156 (基于 19 个变量)
  处理行业: '煤炭' (12 个变量) 对 Factor9...
  => Factor9 对行业 '煤炭' 的 Pooled R²: 0.1192 (基于 12 个变量)
  处理行业: '钢铁' (12 个变量) 对 Factor9...
  => Factor9 对行业 '钢铁' 的 Pooled R²: 0.0910 (基于 12 个变量)
  处理行业: '油气' (3 个变量) 对 Factor9...
  => Factor9 对行业 '油气' 的 Pooled R²: 0.0751 (基于 3 个变量)
  处理行业: '汽车' (2 个变量) 对 Factor9...
  => Factor9 对行业 '汽车' 的 Pooled R²: 0.0049 (基于 2 个变量)
  处理行业: '电力' (3 个变量) 对 Factor9...
  => Factor9 对行业 '电力' 的 Pooled R²: 0.0244 (基于 3 个变量)
  处理行业: '制造业' (4 个变量) 对 Factor9...
  => Factor9 对行业 '制造业' 的 Pooled R²: 0.1460 (基于 4 个变量)
  处理行业: '工业' (1 个变量) 对 Factor9...
  => Factor9 对行业 '工业' 的 Pooled R²: 0.1243 (基于 1 个变量)
[调试类型R2计算] 检查通过: var_type_map 有效 (大小: 166), 准备调用 calculate_factor_type_r2。
开始计算单因子对变量类型的 Pooled R² (OLS-based)...
将为 9 个因子和 4 个类型计算 Pooled R² (OLS-based)...
单因子对变量类型的 Pooled R² (OLS-based) 计算完成。
[调试类型R2计算] 检查通过: var_type_map 有效 (大小: 166), 准备调用 calculate_factor_type_r2。
开始计算单因子对变量类型的 Pooled R² (OLS-based)...
将为 9 个因子和 4 个类型计算 Pooled R² (OLS-based)...
单因子对变量类型的 Pooled R² (OLS-based) 计算完成。
成功提取最终模型状态转移矩阵 A 的特征根 (模长)，数量: 9
--- 调用 analyze_and_save_final_results ---
开始分析最终结果并写入 Excel: dym_estimate\dfm_result\final_results_20250616_105030.xlsx
计算对月底目标的 Nowcast 序列 y_{T(t)|t} (使用 final_dfm_results.x 和 A)...
Target date T(t)=2025-06-27 00:00:00 not in filtered_state index for t=2025-06-06 00:00:00. Calculating k based on days/7.
对月底目标的 Nowcast 序列 y_{T(t)|t} 计算完成。
计算平滑 Nowcast 序列 (使用 .x_sm, 用于对比)...
平滑 Nowcast 序列计算完成。
  [DEBUG] Filtered Nowcast (for metrics) Index Range: 2019-12-27 00:00:00 to 2025-06-06 00:00:00
Filtered Nowcast series (for reporting) to start from 2020-01-01. Shape: (284,)
计算最终模型的 IS/OOS RMSE 和 Hit Rate (使用 Filtered Nowcast 和 analysis_utils)...
最终模型评估指标 (基于 Filtered Nowcast) 计算完成: {'is_rmse': np.float64(1.8301717475716959), 'oos_rmse': np.float64(0.6779181219048273), 'is_mae': 1.4532061141404837, 'oos_mae': 0.5500252062041071, 'is_hit_rate': np.float64(72.16216216216216), 'oos_hit_rate': np.float64(81.0)}
准备写入 Excel 文件: dym_estimate\dfm_result\final_results_20250616_105030.xlsx
元数据文件未找到: dym_estimate\dfm_result\final_dfm_metadata.pkl
  正在写入 '指标解释' Sheet...
  '指标解释' Sheet 写入完成。
  正在写入 'Summary' Sheet...
  'Summary' Sheet 写入完成。
  正在写入 'Monthly Forecast vs Target' Sheet...
开始创建对齐的 Nowcast vs Target 表格...
成功创建对齐表格，包含 67 行数据。
  'Monthly Forecast vs Target' Sheet 写入完成。
  正在写入 'Factor Time Series' Sheet...
  'Factor Time Series' Sheet 写入完成。
  正在写入 'R2 Analysis Combined' Sheet...
[Debug Write R2] Received 'factor_type_r2'. Type: <class 'dict'>. Is None or Empty: False
[Debug Write R2] 'factor_type_r2' keys: ['Factor1', 'Factor2', 'Factor3', 'Factor4', 'Factor5', 'Factor6', 'Factor7', 'Factor8', 'Factor9']
  'R2 Analysis Combined' Sheet 写入完成。
  正在写入 'Variables and Loadings' Sheet...
  'Variables and Loadings' Sheet 写入完成。
Excel 文件写入完成: dym_estimate\dfm_result\final_results_20250616_105030.xlsx
开始绘制最终图形到目录: dym_estimate\dfm_result\plots
  绘制 Filtered Nowcast vs Target 图...

[绘图函数] 生成最终 Nowcasting 图: dym_estimate\dfm_result\plots\20250616_105030_final_filtered_nowcast_vs_target.png...
  已屏蔽 1月/2月 的实际观测值用于绘图。
最终 Nowcasting 图已保存到: dym_estimate\dfm_result\plots\20250616_105030_final_filtered_nowcast_vs_target.png
  Filtered Nowcast vs Target 图绘制完成。
  绘制最终因子载荷聚类图...
\n[绘图函数] 开始生成因子载荷聚类热力图: dym_estimate\dfm_result\plots\20250616_105030_final_factor_loadings_clustermap.png...
变量数量较多 (>50) 且未指定 top_n_vars，默认禁用数值标注。
因子载荷聚类热力图已保存至: dym_estimate\dfm_result\plots\20250616_105030_final_factor_loadings_clustermap.png
  因子载荷聚类图绘制完成。
  绘制行业 vs 主要驱动因子图...
开始绘制行业与主要驱动因子对比图 (Log 处理行业变量(左轴), 原始因子(右轴))...
[绘图调试] 找到 11 个唯一行业: ['制造业', '化学化工', '化纤', '工业', '橡胶塑料', '汽车', '油气', '煤炭', '电力', '运输']...
  未能确定行业 '运输' 的驱动因子 (可能所有因子 R² 都无效或为负)。
  [Plot Loop] 开始处理行业: 制造业
  [Plot Loop] 完成处理行业: 制造业
  [Plot Loop] 开始处理行业: 化学化工
  [Plot Loop] 完成处理行业: 化学化工
  [Plot Loop] 开始处理行业: 化纤
  [Plot Loop] 完成处理行业: 化纤
  [Plot Loop] 开始处理行业: 工业
  [Plot Loop] 完成处理行业: 工业
  [Plot Loop] 开始处理行业: 橡胶塑料
  [Plot Loop] 完成处理行业: 橡胶塑料
  [Plot Loop] 开始处理行业: 汽车
  [Plot Loop] 完成处理行业: 汽车
  [Plot Loop] 开始处理行业: 油气
  [Plot Loop] 完成处理行业: 油气
  [Plot Loop] 开始处理行业: 煤炭
  [Plot Loop] 完成处理行业: 煤炭
  [Plot Loop] 开始处理行业: 电力
  [Plot Loop] 完成处理行业: 电力
  [Plot Loop] 开始处理行业: 钢铁
  [Plot Loop] 完成处理行业: 钢铁
隐藏未使用的子图轴...
设置主标题...
调整布局 (tight_layout)...
布局调整完成。
即将保存行业驱动因子图到: dym_estimate\dfm_result\plots\20250616_105030_final_industry_driving_factors.png
行业与驱动因子对比图已保存至: dym_estimate\dfm_result\plots\20250616_105030_final_industry_driving_factors.png
关闭绘图对象...
绘制行业与驱动因子对比图完成。
  行业 vs 主要驱动因子图绘制完成。
最终结果分析和保存完成。
Added 'factor_loadings_df' to returned metrics.
Added 'nowcast_aligned' to returned metrics.
Added 'y_test_aligned' to returned metrics.
analyze_and_save_final_results 调用完成。返回的指标: {'is_rmse': np.float64(1.8301717475716959), 'oos_rmse': np.float64(0.6779181219048273), 'is_mae': 1.4532061141404837, 'oos_mae': 0.5500252062041071, 'is_hit_rate': np.float64(72.16216216216216), 'oos_hit_rate': np.float64(81.0), 'factor_loadings_df':                       Factor1   Factor2   Factor3   Factor4   Factor5   Factor6   Factor7   Factor8   Factor9
MEG：产能利用率：中国（周）     -0.169905  0.413003 -0.013539  0.197065 -0.016322  0.251543 -0.213999 -0.375282  0.026901
MEG：产量：中国（周）         0.018720  0.067958  0.079901  0.056851  0.031089  0.122041  0.020973 -0.073126 -0.104770
PE：产能利用率：中国（周）      -0.132806  0.304194  0.480188  0.501630 -0.167098  0.274081 -0.482253 -0.433361 -0.344039
PE：化工生产企业：产量：中国（周）   0.023170  0.028707  0.095927  0.121996  0.047412  0.023249 -0.104164 -0.012927 -0.003721
PE：社会库存：中国（周）       -0.003466  0.135134  0.145925  0.146896  0.054735  0.088092 -0.238418 -0.187846 -0.092968
...                       ...       ...       ...       ...       ...       ...       ...       ...       ...
精煤：523家样本矿山：日均产量（周） -0.010025 -0.339164 -0.177290 -0.034966 -0.049846  0.133511  0.028560 -0.268023  0.057501
聚酯：产能利用率：中国（周）      -0.433971 -0.095398 -0.295268 -0.182489 -0.459828 -0.116436  0.166239  0.040014 -0.020379
聚酯：产量：中国（周）         -0.111408 -0.015741 -0.068823  0.262584 -0.249975  0.216218 -0.024072 -0.164387 -0.291489
规模以上工业增加值:当月同比      -0.300215  0.098568 -0.060105 -0.037978 -0.325994 -0.285778  0.155218  0.055542 -0.155804
重点电厂:日耗量:煤炭          0.029522  0.074207  0.224397  0.256785 -0.122864  0.101778 -0.578775  0.004217 -0.204153

[72 rows x 9 columns], 'nowcast_aligned': 2019-12-27    4.172642
2020-01-03    2.522863
2020-01-10    1.925807
2020-01-17    1.638615
2020-01-24    2.605475
                ...   
2025-05-09    5.218794
2025-05-16    4.841058
2025-05-23    5.506003
2025-05-30    5.463742
2025-06-06    3.674210
Name: Nowcast, Length: 285, dtype: float64, 'y_test_aligned': 2019-12-27    NaN
2020-01-03    NaN
2020-01-10    NaN
2020-01-17    NaN
2020-01-24    NaN
             ... 
2025-05-09    6.1
2025-05-16    6.1
2025-05-23    6.1
2025-05-30    6.1
2025-06-06    NaN
Name: 规模以上工业增加值:当月同比, Length: 285, dtype: float64}
--- [Debug Meta Build Check] ---
[Debug Meta Build Check] Type of all_data_aligned_weekly: <class 'pandas.core.frame.DataFrame'>
[Debug Meta Build Check] all_data_aligned_weekly is None? False
[Debug Meta Build Check] Shape of all_data_aligned_weekly: (285, 76)
[Debug Meta Build Check] Type of target_mean_original: <class 'numpy.float64'>
[Debug Meta Build Check] target_mean_original is None? False
[Debug Meta Build Check] Value of target_mean_original: 5.063461538461539
[Debug Meta Build Check] Type of target_std_original: <class 'numpy.float64'>
[Debug Meta Build Check] target_std_original is None? False
[Debug Meta Build Check] Value of target_std_original: 2.545586634199904
[Debug Meta Build Check] Type of final_data_processed: <class 'pandas.core.frame.DataFrame'>
[Debug Meta Build Check] final_data_processed is None? False
[Debug Meta Build Check] Shape of final_data_processed: (285, 72)
--- [Debug Meta Build Check End] ---
--- [Debug Final Save Check - pca_results_df specific] ---
[Debug Final Save Check] Type of pca_results_df IN METADATA before dump: <class 'pandas.core.frame.DataFrame'>
[Debug Final Save Check] Shape of pca_results_df IN METADATA before dump: (9, 4)
[Debug Final Save Check] Columns of pca_results_df IN METADATA before dump: ['主成分 (PC)', '解释方差 (%)', '累计解释方差 (%)', '特征值 (Eigenvalue)']
[Debug Final Save Check] '特征值 (Eigenvalue)' column EXISTS in pca_results_df.
--- [Debug Final Save Check - pca_results_df specific End] ---

--- 正在仅训练期数据上重新运行最终选定模型以进行稳定性分析 --- 
已截取训练期处理后数据 (最终变量列，截至 2024-06-28)，形状: (236, 72)
假设训练期数据已使用合适的（如全样本的）标准化参数处理。
开始在训练期数据上运行 DFM (k=9)...
训练期 DFM 模型实例化（即拟合）完成。
[Debug Train DFM] train_dfm_model type: <class 'DynamicFactorModel.DFMEMResultsWrapper'>
[Debug Train DFM] Attributes of train_dfm_model: ['A', 'B', 'Lambda', 'P0', 'Q', 'R', '__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__firstlineno__', '__format__', '__ge__', '__getattribute__', '__getstate__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__setattr__', '__sizeof__', '__static_attributes__', '__str__', '__subclasshook__', '__weakref__', 'obs_mean', 'x', 'x0', 'x_sm', 'z']
[Debug Train DFM] Has 'x_sm' attribute? True
[Debug Train DFM] Has 'Lambda' attribute? True
[Debug Train DFM] Type of factors attribute: <class 'pandas.core.frame.DataFrame'>
[Debug Train DFM] Shape of factors: (236, 9)
[Debug Train DFM] Type of loadings attribute: <class 'numpy.ndarray'>
[Debug Train DFM] Shape of loadings: (72, 9)
已提取训练期因子时间序列 (来自 DataFrame)。
已提取训练期因子载荷矩阵 (来自 NumPy 数组)。
已将训练期载荷和因子序列添加到待保存的元数据中。
已将 'best_k_factors' (9) 添加到元数据。
开始保存最终模型到: dym_estimate\dfm_result\final_dfm_model.joblib
最终模型保存成功。
开始保存元数据到: dym_estimate\dfm_result\final_dfm_metadata.pkl
--- [Debug Final Save Check] ---
[Debug Final Save Check] Type of training_lambda IN METADATA before dump: <class 'pandas.core.frame.DataFrame'>
[Debug Final Save Check] Type of training_factors IN METADATA before dump: <class 'pandas.core.frame.DataFrame'>
[Debug Final Save Check] Shape of training_lambda IN METADATA before dump: (72, 9)
[Debug Final Save Check] Shape of training_factors IN METADATA before dump: (236, 9)
--- [Debug Final Save Check End] ---
[Debug Final Save Check] Added 'training_start_date' to metadata: 2020-01-01
已将 'x0' (initial_state) 和 'P0' (initial_state_cov) 添加到元数据。
元数据保存成功。

--- 调优和最终模型估计完成 --- 总耗时: 1006.90 秒 ---
