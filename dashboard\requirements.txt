# Core Data Science Libraries
pandas>=1.5.0
numpy>=1.21.0
scipy>=1.8.0

# Machine Learning
scikit-learn>=1.2.0
statsmodels>=0.13.0

# Visualization
matplotlib>=3.5.0
seaborn>=0.12.0
plotly>=5.13.0
altair>=4.2.0

# Web Framework
streamlit>=1.24.0

# File Handling
openpyxl>=3.0.10

# Performance and Optimization
numba>=0.56.0
joblib>=1.2.0

# Progress Bars
tqdm>=4.65.0

# Time Series Analysis
fastdtw>=0.3.4

# Date/Time Utilities
python-dateutil>=2.8.0

# Kalman Filter (for DFM)
filterpy>=1.4.5

# Note: Standard library modules don't need to be included:
# os, sys, logging, warnings, traceback, pickle, datetime, time
# math, calendar, threading, concurrent.futures, collections
# typing, re, io, unicodedata, argparse