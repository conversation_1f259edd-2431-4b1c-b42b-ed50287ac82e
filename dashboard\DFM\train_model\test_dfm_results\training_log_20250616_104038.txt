=== 🚀 启动DFM训练管道 ===
目标变量: 规模以上工业增加值:当月同比
选择的指标数量: 75
🔥 因子选择范围: (1, 20)
🔥 启用变量选择: True
🔥 启用超参数调优: True

=== 📋 参数传递验证 ===
📅 时间参数:
  - 训练开始日期: 2020-01-01
  - 训练结束日期: 2024-06-28
  - 验证开始日期: 2024-07-05
  - 验证结束日期: 2024-12-27
🎯 模型参数:
  - 因子数量: 3
  - 因子阶数: 1
  - 特异性自回归阶数: 1
  - EM最大迭代次数: 30
🔧 优化参数:
  - 变量选择方法: global_backward
  - 信息准则方法: bic
  - 累积方差阈值: 0.8
  - 使用Bai-Ng因子选择: True
📊 数据映射:
  - 行业映射对象: 已提供
  - 变量类型映射: 未提供
    行业映射数量: 76
📁 输出设置:
  - 输出基础目录: test_outputs
  - 启用详细分析: False
  - 生成Excel报告: False

=== ⚠️ 关键诊断信息 ===
🔍 _MODULES_AVAILABLE 状态: True
🔍 输入数据形状: (262, 76)
🔍 选择指标列表: ['中国:日均产量:生铁:重点企业', '中国:日均产量:粗钢:重点企业', '重点电厂:日耗量:煤炭', '中国:主要45港口:日均疏港量:铁矿石', '中国:生产率:焦炉:国内独立焦化厂(230家)']...（前5个）
🔍 模块导入状态:
    - dfm_core: ✅ 成功
    - DynamicFactorModel: ✅ 成功
    - analysis_utils: ✅ 成功
    - variable_selection: ✅ 成功
    - results_analysis: ✅ 成功
步骤1: 数据准备...
使用传入的input_df进行训练
🔍🔍🔍 关键变量选择诊断:
    - 原始selected_indicators长度: 75
    - 目标变量: 规模以上工业增加值:当月同比
    - 最终available_cols长度: 76
    - available_cols内容: ['规模以上工业增加值:当月同比', '中国:日均产量:生铁:重点企业', '中国:日均产量:粗钢:重点企业', '重点电厂:日耗量:煤炭', '中国:主要45港口:日均疏港量:铁矿石', '中国:生产率:焦炉:国内独立焦化厂(230家)', '中国:高炉开工率(247家)', '中国:产量:螺纹钢:主要钢厂', '中国:开工率:线材:主要钢厂', '中国:产量:线材:主要钢厂', '中国:产量:冷轧板卷:主要钢厂', '中国:产量:热轧板卷:主要钢厂', '中国:产量:中厚板:主要钢厂', '中国:产量:钢材:重点钢铁企业', '中国:开工率:产能(>200万吨):焦化企业(230家)', '中国:开工率:产能(100-200万吨):焦化企业(230家)', '中国:主流港口:库存量:煤炭', '中国:库存量:焦炭:国内样本钢厂(247家)', '动力煤：462家样本矿山：产能利用率（周）', '动力煤：462家样本矿山：日均产量（周）', '精煤：样本洗煤厂（110家）：日均产量：中国（周）', '煤炭：样本洗煤厂（110家）：开工率：中国（周）', '精煤：523家样本矿山：日均产量（周）', '原煤：523家样本矿山：日均产量（周）', '炼焦煤：523家样本矿山：开工率（周）', '焦炭：230家独立焦化厂：产能利用率：中国（周）', '焦炭：230家独立焦化厂：日均产量：中国（周）', '中国:开工率(常减压开工率):山东地炼厂', '中国:产能利用率:成品油:独立炼厂', '中国:产能利用率:成品油:主营炼厂', '乙烯：MTO：生产企业：产能利用率：中国（周）', '乙烯：轻烃裂解：生产企业：产能利用率：中国（周）', '乙烯：石脑油裂解：生产企业：产能利用率：中国（周）', '乙烯：MTO：产量：中国（周）', '乙烯：产量：中国（周）', '乙烯：轻烃裂解：产量：中国（周）', '乙烯：石脑油裂解：产量：中国（周）', '乙烯：市场价：华东地区（周）', '氯化铵：产量：中国（周）', '氯化铵：产能利用率：中国（周）', '三聚氰胺：产量：中国（周）', '三聚氰胺：产能利用率：中国（周）', '尿素：产量：中国（周）', '尿素：产能利用率：中国（周）', '磷酸一铵：工业级：产能利用率：中国（周）', '磷酸一铵：工业级：产量：中国（周）', '磷酸一铵：产量：中国（周）', '磷酸二铵：产量：中国（周）', '硫酸钾：开工率：中国（周）', '氯化钾：产能利用率：中国（周）', '中国:开工率:精对苯二甲酸', '中国:江浙地区:开工率:涤纶长丝', '中国:装置负荷率:涤纶短纤', 'PTA：产能利用率：中国（周）', 'PTA：产量：中国（周）', 'MEG：产能利用率：中国（周）', 'MEG：产量：中国（周）', '聚酯：产能利用率：中国（周）', '聚酯：产量：中国（周）', 'PE：产能利用率：中国（周）', 'PE：化工生产企业：产量：中国（周）', 'PE：社会库存：中国（周）', 'PP：注塑：开工率：中国（周）', 'PP：产能利用率：中国（周）', 'PP：产量：中国（周）', 'PVC：产能利用率：中国（周）', 'PVC：产量：中国（周）', '中国:开工率:汽车轮胎(半钢胎)', '中国:开工率:汽车轮胎(全钢胎)', '制造业PMI', '制造业PMI:生产', '制造业PMI:新订单', '制造业PMI:新出口订单', '制造业PMI:从业人员', '中国：可再生能源：发电量（月）', '中国：火力发电：发电量（月）']
数据子集形状: (262, 76)
时间范围: 2019-12-27 00:00:00 到 2024-12-27 00:00:00
时间过滤后数据形状: (261, 76)
步骤2: 数据标准化...
步骤3: 初始因子数估计（第一阶段）...
🔥 第一阶段：使用全体变量进行Bai-Ng因子选择...
开始Bai-Ng ICp2因子选择，最大因子数: 20
Bai-Ng参数: N=76, T=261, k_max=20
Bai-Ng ICp2最优因子数: 19
ICp2值: ['k=1: -0.5901', 'k=2: -0.6823', 'k=3: -0.7395', 'k=4: -0.7914', 'k=5: -0.8440', 'k=6: -0.8883', 'k=7: -0.9295', 'k=8: -0.9745', 'k=9: -1.0263', 'k=10: -1.0896']
⚠️ 初始估计因子数 19 超过上限 10，调整为上限值
🔥 第一阶段选择的初始因子数量: 10
   这个因子数将用于变量选择阶段
步骤4: 变量选择...
🔥 开始全局后向变量选择，初始变量数: 76
   目标变量: 规模以上工业增加值:当月同比
   预测变量数: 75
   变量选择方法: global_backward
🔧 调用全局后向变量选择...
   初始因子数: 10
   目标变量统计: 均值=4.965, 标准差=2.585
✅ 成功导入 evaluate_dfm_params
全局后向筛选：计算初始基准性能...
全局后向筛选：初始基准评估未能计算有效分数。无法继续。
✅ 全局后向变量选择完成!
   最终选择的变量数: 76
   其中预测变量数: 75
   最终得分: HR=-inf%, RMSE=-inf
   总评估次数: 1
   SVD错误次数: 0
步骤4.5: 最终因子数选择（第二阶段）...
🔥 第二阶段：使用筛选后的变量再次进行Bai-Ng因子选择...
开始Bai-Ng ICp2因子选择，最大因子数: 20
Bai-Ng参数: N=76, T=261, k_max=20
Bai-Ng ICp2最优因子数: 19
ICp2值: ['k=1: -0.5901', 'k=2: -0.6823', 'k=3: -0.7395', 'k=4: -0.7914', 'k=5: -0.8440', 'k=6: -0.8883', 'k=7: -0.9295', 'k=8: -0.9745', 'k=9: -1.0263', 'k=10: -1.0896']
🔥 第二阶段最终选择的因子数量: 19
   基于 76 个筛选后的变量
步骤5: 训练最终DFM模型...
