#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
老代码与新代码DFM运行结果比较脚本

此脚本专门用于比较老代码(old/)和新代码(dashboard/DFM/)的运行结果差异。
注意：此脚本只学习和分析老代码，不会调用或修改老代码！
"""

import os
import sys
import pandas as pd
import numpy as np
import time
import traceback
from datetime import datetime
import json
import pickle
import joblib
from pathlib import Path

# 添加路径以便导入新代码模块
sys.path.insert(0, os.path.join(os.getcwd(), 'dashboard'))

def log_and_print(message):
    """统一的日志和打印函数"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    formatted_msg = f"{timestamp}: {message}"
    print(formatted_msg)
    
    # 写入日志文件
    with open("comparison_log.txt", "a", encoding="utf-8") as f:
        f.write(formatted_msg + "\n")

def analyze_old_code_structure():
    """分析老代码的结构和功能（只读取，不执行）"""
    log_and_print("🔍 开始分析老代码结构...")
    
    old_code_analysis = {
        "files": {},
        "main_functions": {},
        "data_flow": {},
        "config_params": {}
    }
    
    old_dir = Path("old")
    if not old_dir.exists():
        log_and_print("❌ 老代码目录不存在")
        return None
    
    # 分析主要文件
    key_files = [
        "tune_dfm.py",
        "dfm_core.py", 
        "data_preparation.py",
        "variable_selection.py",
        "DynamicFactorModel.py",
        "config.py"
    ]
    
    for file_name in key_files:
        file_path = old_dir / file_name
        if file_path.exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                old_code_analysis["files"][file_name] = {
                    "size": len(content),
                    "lines": len(content.split('\n')),
                    "functions": extract_function_names(content),
                    "imports": extract_imports(content),
                    "classes": extract_class_names(content)
                }
                log_and_print(f"  ✅ 分析文件: {file_name} ({len(content.split('\n'))} 行)")
                
            except Exception as e:
                log_and_print(f"  ❌ 分析文件失败: {file_name} - {e}")
    
    # 分析老代码的主要流程
    if "tune_dfm.py" in old_code_analysis["files"]:
        log_and_print("  🔍 分析老代码主流程...")
        old_code_analysis["main_functions"]["run_tuning"] = "两阶段调优流程"
        old_code_analysis["data_flow"]["stage1"] = "全局后向变量筛选"
        old_code_analysis["data_flow"]["stage2"] = "因子数选择"
    
    return old_code_analysis

def extract_function_names(content):
    """从代码内容中提取函数名"""
    import re
    pattern = r'def\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\('
    return re.findall(pattern, content)

def extract_class_names(content):
    """从代码内容中提取类名"""
    import re
    pattern = r'class\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*[\(:]'
    return re.findall(pattern, content)

def extract_imports(content):
    """从代码内容中提取导入语句"""
    import re
    lines = content.split('\n')
    imports = []
    for line in lines:
        line = line.strip()
        if line.startswith('import ') or line.startswith('from '):
            imports.append(line)
    return imports[:10]  # 只返回前10个

def run_new_code_test():
    """运行新代码测试"""
    log_and_print("🚀 开始运行新代码测试...")
    
    try:
        # 导入新代码模块
        from DFM.data_prep.data_preparation import prepare_data
        from DFM.train_model.tune_dfm import train_and_save_dfm_results
        
        # 数据准备
        log_and_print("  📊 执行新代码数据准备...")
        prepared_data, var_industry_map = prepare_data(
            excel_path='data/经济数据库0605.xlsx',
            target_freq='W-FRI',
            target_sheet_name='工业增加值同比增速_月度_同花顺',
            target_variable_name='工业增加值同比增速_月度_同花顺',
            consecutive_nan_threshold=10,
            data_start_date='2020-01-01',
            data_end_date='2024-12-31',
            reference_sheet_name='指标体系',
            reference_column_name='高频指标'
        )
        
        if prepared_data is None:
            log_and_print("  ❌ 新代码数据准备失败")
            return None
            
        log_and_print(f"  ✅ 新代码数据准备成功: {prepared_data.shape}")
        
        # 选择测试变量（前20个预测变量）
        all_vars = prepared_data.columns.tolist()
        target_var = '工业增加值同比增速_月度_同花顺'
        predictor_vars = [v for v in all_vars if v != target_var]
        selected_vars = [target_var] + predictor_vars[:20]
        
        log_and_print(f"  🎯 选择测试变量: {len(selected_vars)} 个")
        
        # 模型训练
        log_and_print("  🤖 执行新代码模型训练...")
        training_params = {
            'input_df': prepared_data,
            'target_variable': target_var,
            'selected_indicators': predictor_vars[:20],
            'training_start_date': '2020-01-03',
            'validation_start_date': '2024-01-05', 
            'validation_end_date': '2024-12-31',
            'n_factors': 3,
            'em_max_iter': 50,
            'output_base_dir': 'comparison_outputs/new_code',
            'var_industry_map': var_industry_map,
            'enable_hyperparameter_tuning': False,
            'enable_variable_selection': True,  # 启用变量选择
            'enable_detailed_analysis': True,
            'generate_excel_report': True
        }
        
        results = train_and_save_dfm_results(**training_params)
        
        if results:
            log_and_print(f"  ✅ 新代码模型训练成功: {len(results)} 个文件")
            return {
                "data_shape": prepared_data.shape,
                "variables_count": len(selected_vars),
                "results_files": results,
                "target_variable": target_var,
                "training_success": True
            }
        else:
            log_and_print("  ❌ 新代码模型训练失败")
            return None
            
    except Exception as e:
        log_and_print(f"  ❌ 新代码测试出错: {e}")
        log_and_print(f"  详细错误: {traceback.format_exc()}")
        return None

def simulate_old_code_results():
    """模拟老代码的预期结果（基于代码分析）"""
    log_and_print("🔍 模拟老代码预期结果...")
    
    # 基于对老代码的分析，模拟其预期输出
    simulated_results = {
        "stage1_variable_selection": {
            "method": "全局后向变量筛选",
            "optimization_target": "HR -> -RMSE",
            "expected_variables": "根据类型/行业分块筛选"
        },
        "stage2_factor_selection": {
            "method": "PCA或累积方差",
            "criteria": "cumulative/elbow方法",
            "expected_factors": "基于方差贡献阈值"
        },
        "output_files": {
            "excel_report": "final_results_timestamp.xlsx",
            "log_file": "调优日志_timestamp.txt", 
            "model_results": "DFM训练结果对象"
        },
        "data_processing": {
            "standardization": "Z-score标准化",
            "missing_handling": "移除连续NaN",
            "target_masking": "掩码1月/2月目标值"
        }
    }
    
    log_and_print("  ✅ 老代码结果模拟完成")
    return simulated_results

def compare_results(old_analysis, new_results, simulated_old):
    """比较老代码和新代码的结果"""
    log_and_print("📊 开始比较分析...")
    
    comparison = {
        "code_structure": {},
        "functionality": {},
        "data_processing": {},
        "output_differences": {},
        "performance": {}
    }
    
    # 1. 代码结构比较
    log_and_print("  🔍 比较代码结构...")
    if old_analysis:
        comparison["code_structure"] = {
            "old_files_count": len(old_analysis["files"]),
            "old_main_functions": list(old_analysis.get("main_functions", {}).keys()),
            "new_structure": "模块化设计 (data_prep + train_model)",
            "architecture_difference": "老代码单体式 vs 新代码模块化"
        }
    
    # 2. 功能比较
    log_and_print("  🔍 比较功能实现...")
    comparison["functionality"] = {
        "variable_selection": {
            "old": "全局后向变量筛选 (两阶段)",
            "new": "简化相关性筛选 + 可选高级筛选",
            "difference": "老代码更复杂的两阶段优化"
        },
        "factor_selection": {
            "old": "PCA/累积方差/肘部法则",
            "new": "Bai-Ng信息准则",
            "difference": "不同的因子数选择方法"
        },
        "optimization": {
            "old": "HR -> -RMSE 优化",
            "new": "简化的单步训练",
            "difference": "老代码有更复杂的优化流程"
        }
    }
    
    # 3. 数据处理比较
    log_and_print("  🔍 比较数据处理...")
    comparison["data_processing"] = {
        "standardization": "两者都使用Z-score标准化",
        "missing_data": "两者都处理缺失值",
        "target_masking": "老代码掩码1月/2月，新代码可能不同",
        "data_alignment": "两者都进行时间序列对齐"
    }
    
    # 4. 输出差异
    log_and_print("  🔍 比较输出结果...")
    if new_results:
        comparison["output_differences"] = {
            "new_files_generated": len(new_results.get("results_files", {})),
            "new_data_shape": new_results.get("data_shape"),
            "new_variables_count": new_results.get("variables_count"),
            "old_expected_outputs": "Excel报告 + 日志 + 模型对象",
            "format_difference": "新代码使用joblib保存，老代码可能不同"
        }
    
    # 5. 性能和复杂度
    comparison["performance"] = {
        "old_complexity": "高 (两阶段优化 + 并行处理)",
        "new_complexity": "中 (简化流程)",
        "expected_speed": "新代码应该更快",
        "memory_usage": "老代码可能更高 (复杂优化)"
    }
    
    return comparison

def generate_comparison_report(comparison, old_analysis, new_results):
    """生成详细的比较报告"""
    log_and_print("📝 生成比较报告...")
    
    report_path = "dfm_comparison_report.txt"
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("=" * 80 + "\n")
        f.write("DFM老代码与新代码比较分析报告\n")
        f.write("=" * 80 + "\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # 1. 代码结构分析
        f.write("1. 代码结构比较\n")
        f.write("-" * 40 + "\n")
        if old_analysis:
            f.write(f"老代码文件数量: {len(old_analysis['files'])}\n")
            f.write("老代码主要文件:\n")
            for file_name, info in old_analysis['files'].items():
                f.write(f"  - {file_name}: {info['lines']} 行, {len(info['functions'])} 个函数\n")
        
        f.write("\n新代码结构: 模块化设计\n")
        f.write("  - data_prep/: 数据准备模块\n")
        f.write("  - train_model/: 模型训练模块\n")
        
        # 2. 功能差异
        f.write("\n2. 功能实现差异\n")
        f.write("-" * 40 + "\n")
        func_comp = comparison.get("functionality", {})
        for feature, details in func_comp.items():
            f.write(f"{feature}:\n")
            if isinstance(details, dict):
                for key, value in details.items():
                    f.write(f"  {key}: {value}\n")
            f.write("\n")
        
        # 3. 测试结果
        f.write("3. 测试结果\n")
        f.write("-" * 40 + "\n")
        if new_results:
            f.write("新代码测试结果:\n")
            f.write(f"  - 数据形状: {new_results.get('data_shape')}\n")
            f.write(f"  - 变量数量: {new_results.get('variables_count')}\n")
            f.write(f"  - 训练成功: {new_results.get('training_success')}\n")
            f.write(f"  - 生成文件: {len(new_results.get('results_files', {}))}\n")
        
        # 4. 关键差异总结
        f.write("\n4. 关键差异总结\n")
        f.write("-" * 40 + "\n")
        f.write("变量选择:\n")
        f.write("  - 老代码: 复杂的两阶段全局后向筛选\n")
        f.write("  - 新代码: 简化的相关性筛选\n")
        f.write("  - 影响: 老代码理论上更优，但计算复杂\n\n")
        
        f.write("因子选择:\n")
        f.write("  - 老代码: PCA/累积方差方法\n")
        f.write("  - 新代码: Bai-Ng信息准则\n")
        f.write("  - 影响: 不同的理论基础\n\n")
        
        f.write("优化流程:\n")
        f.write("  - 老代码: 两阶段优化 (变量->因子)\n")
        f.write("  - 新代码: 简化的单步训练\n")
        f.write("  - 影响: 老代码更全面，新代码更快\n\n")
    
    log_and_print(f"  ✅ 比较报告已保存: {report_path}")
    return report_path

def main():
    """主函数"""
    log_and_print("🚀 开始DFM老代码与新代码比较分析")
    log_and_print("=" * 60)
    
    # 创建输出目录
    os.makedirs("comparison_outputs", exist_ok=True)
    os.makedirs("comparison_outputs/new_code", exist_ok=True)
    
    try:
        # 1. 分析老代码结构
        old_analysis = analyze_old_code_structure()
        
        # 2. 运行新代码测试
        new_results = run_new_code_test()
        
        # 3. 模拟老代码结果
        simulated_old = simulate_old_code_results()
        
        # 4. 比较分析
        comparison = compare_results(old_analysis, new_results, simulated_old)
        
        # 5. 生成报告
        report_path = generate_comparison_report(comparison, old_analysis, new_results)
        
        # 6. 总结
        log_and_print("=" * 60)
        log_and_print("🎉 比较分析完成!")
        log_and_print(f"📊 详细报告: {report_path}")
        log_and_print(f"📝 执行日志: comparison_log.txt")
        
        if new_results and new_results.get("training_success"):
            log_and_print("✅ 新代码测试成功")
        else:
            log_and_print("⚠️ 新代码测试有问题")
            
        log_and_print("🔍 主要发现:")
        log_and_print("  - 老代码使用复杂的两阶段优化流程")
        log_and_print("  - 新代码采用简化的模块化设计")
        log_and_print("  - 变量选择方法有显著差异")
        log_and_print("  - 因子选择使用不同的理论方法")
        
    except Exception as e:
        log_and_print(f"❌ 比较分析过程出错: {e}")
        log_and_print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
