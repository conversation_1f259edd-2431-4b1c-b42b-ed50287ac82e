================================================================================
DFM老代码与新代码比较分析报告
================================================================================
生成时间: 2025-06-16 10:13:01

1. 代码结构比较
----------------------------------------
老代码文件数量: 6
老代码主要文件:
  - tune_dfm.py: 1987 行, 1 个函数
  - dfm_core.py: 393 行, 1 个函数
  - data_preparation.py: 1747 行, 7 个函数
  - variable_selection.py: 563 行, 2 个函数
  - DynamicFactorModel.py: 340 行, 6 个函数
  - config.py: 112 行, 0 个函数

新代码结构: 模块化设计
  - data_prep/: 数据准备模块
  - train_model/: 模型训练模块

2. 功能实现差异
----------------------------------------
variable_selection:
  old: 全局后向变量筛选 (两阶段)
  new: 简化相关性筛选 + 可选高级筛选
  difference: 老代码更复杂的两阶段优化

factor_selection:
  old: PCA/累积方差/肘部法则
  new: Bai-Ng信息准则
  difference: 不同的因子数选择方法

optimization:
  old: HR -> -RMSE 优化
  new: 简化的单步训练
  difference: 老代码有更复杂的优化流程

3. 测试结果
----------------------------------------

4. 关键差异总结
----------------------------------------
变量选择:
  - 老代码: 复杂的两阶段全局后向筛选
  - 新代码: 简化的相关性筛选
  - 影响: 老代码理论上更优，但计算复杂

因子选择:
  - 老代码: PCA/累积方差方法
  - 新代码: Bai-Ng信息准则
  - 影响: 不同的理论基础

优化流程:
  - 老代码: 两阶段优化 (变量->因子)
  - 新代码: 简化的单步训练
  - 影响: 老代码更全面，新代码更快

